import NetworkGraph from '@/components/NetworkGraph';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import {
  Button,
  Card,
  Collapse,
  Descriptions,
  Empty,
  Modal,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './styles.less';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

export interface MapDetailModalProps {
  visible: boolean;
  data: any | null;
  onClose: () => void;
}

interface RelationGroup {
  relationName: string;
  relationCode: string;
  relations: API.ElementRelation[];
}

const MapDetailModal: React.FC<MapDetailModalProps> = ({
  visible,
  data,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );
  const [showAllRelations, setShowAllRelations] = useState(false);

  // 获取类型名称
  const getTypeName = (type?: string) => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'water_system':
      case 'waterSystem':
        return '水系';
      case 'historical_element':
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  // 获取类型颜色
  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'mountain':
        return '#8B4513';
      case 'water_system':
      case 'waterSystem':
        return '#4169E1';
      case 'historical_element':
      case 'historicalElement':
        return '#DC143C';
      default:
        return '#666';
    }
  };

  // 获取目标要素名称
  const getTargetElementName = (relation: API.ElementRelation): string => {
    if (relation.targetElement) {
      return relation.targetElement.name || '未知要素';
    }
    if (relation.sourceElement) {
      return relation.sourceElement.name || '未知要素';
    }
    return '未知要素';
  };

  // 获取目标要素类型名称
  const getTargetElementTypeName = (relation: API.ElementRelation): string => {
    const type = relation.targetEntityType || relation.sourceType;
    return getTypeName(type);
  };

  // 获取节点颜色
  const getNodeColor = (type: string): string => {
    switch (type) {
      case 'mountain':
        return '#8B4513';
      case 'water_system':
      case 'waterSystem':
        return '#4169E1';
      case 'historical_element':
      case 'historicalElement':
        return '#DC143C';
      default:
        return '#666';
    }
  };

  // 获取连线颜色
  const getLinkColor = (relationName: string): string => {
    switch (relationName) {
      case '地理关联':
        return '#52c41a';
      case '历史关联':
        return '#1890ff';
      case '文化关联':
        return '#722ed1';
      default:
        return '#999';
    }
  };

  // 转换关联关系数据为网络图数据
  const convertRelationsToNetworkData = (
    relations: API.ElementRelation[],
    currentElement: any,
  ): API.NetworkGraphData => {
    const nodes: API.NetworkGraphNode[] = [];
    const links: API.NetworkGraphLink[] = [];
    const nodeMap = new Map<string, API.NetworkGraphNode>();

    // 添加当前要素作为中心节点
    const currentNodeId = `${currentElement.type}_${currentElement.id}`;
    const currentNode: API.NetworkGraphNode = {
      id: currentNodeId,
      name: currentElement.name || '当前要素',
      type:
        currentElement.type === 'waterSystem'
          ? 'water_system'
          : currentElement.type === 'historicalElement'
          ? 'historical_element'
          : currentElement.type || 'unknown',
      category: getTypeName(currentElement.type),
      size: 20, // 中心节点较大
      color: getNodeColor(currentElement.type),
    };
    nodeMap.set(currentNodeId, currentNode);

    // 处理关联关系
    relations.forEach((relation) => {
      // 确定源节点和目标节点
      const sourceNodeId = `${relation.sourceType}_${relation.sourceId}`;
      const targetNodeId = `${relation.targetEntityType}_${relation.targetId}`;

      // 添加目标要素节点
      if (!nodeMap.has(targetNodeId)) {
        const targetNode: API.NetworkGraphNode = {
          id: targetNodeId,
          name: getTargetElementName(relation),
          type: relation.targetEntityType || 'unknown',
          category: getTargetElementTypeName(relation),
          size: 15,
          color: getNodeColor(relation.targetEntityType || 'unknown'),
        };
        nodeMap.set(targetNodeId, targetNode);
      }

      // 创建连线
      const link: API.NetworkGraphLink = {
        source: sourceNodeId,
        target: targetNodeId,
        relation: relation.relationDict?.relationName || '其他关系',
        direction: relation.direction,
        term: relation.term,
        weight: 1,
        color: getLinkColor(relation.relationDict?.relationName || '其他关系'),
      };
      links.push(link);
    });

    nodes.push(...Array.from(nodeMap.values()));

    return {
      nodes,
      links,
      categories: ['山塬', '水系', '历史要素'],
    };
  };

  // 获取关联信息
  const fetchRelations = async () => {
    if (!data || !data.type || !data.id) return;

    setLoading(true);
    try {
      // 转换类型名称
      const elementType =
        data.type === 'waterSystem'
          ? 'water_system'
          : data.type === 'historicalElement'
          ? 'historical_element'
          : data.type;

      const response = await getPublicElementRelationsByElement(
        elementType,
        data.id,
      );

      if (response.errCode === 0 && response.data) {
        setRelations(response.data);
        // 转换为网络图数据
        const networkGraphData = convertRelationsToNetworkData(
          response.data,
          data,
        );
        setNetworkData(networkGraphData);
      } else {
        console.error('获取关联信息失败:', response);
        setRelations([]);
        setNetworkData(null);
      }
    } catch (error) {
      console.error('获取关联信息异常:', error);
      setRelations([]);
      setNetworkData(null);
    } finally {
      setLoading(false);
    }
  };

  // 按关系类型分组关联信息
  const groupRelationsByType = (relations: API.ElementRelation[]) => {
    const groupMap = new Map<string, RelationGroup>();

    relations.forEach((relation) => {
      const key = relation.relationDict?.relationCode || 'unknown';
      if (!groupMap.has(key)) {
        groupMap.set(key, {
          relationName: relation.relationDict?.relationName || '未知关系',
          relationCode: key,
          relations: [],
        });
      }
      groupMap.get(key)!.relations.push(relation);
    });

    return Array.from(groupMap.values());
  };

  useEffect(() => {
    if (visible && data) {
      fetchRelations();
    } else {
      setRelations([]);
      setNetworkData(null);
      setShowAllRelations(false);
    }
  }, [visible, data]);

  if (!data) return null;

  const relationGroups = groupRelationsByType(relations);
  const displayRelations = showAllRelations
    ? relationGroups
    : relationGroups.slice(0, 3);

  return (
    <Modal
      title={`${data.name} - 详细信息`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
      style={{ top: 20 }}
      className="map-detail-modal"
    >
      <div
        style={{ maxHeight: '80vh', overflowY: 'auto' }}
        onWheel={(e) => {
          // 阻止滚轮事件冒泡到全局，防止触发全页滚动
          e.stopPropagation();
        }}
      >
        {/* 基本信息 */}
        <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="名称">{data.name}</Descriptions.Item>
            <Descriptions.Item label="类型">
              <Tag color={getTypeColor(data.type)}>
                {getTypeName(data.type)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="编号">
              {data.code || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="坐标">
              {data.longitude && data.latitude
                ? `${data.longitude}, ${data.latitude}`
                : '-'}
            </Descriptions.Item>
            {data.summary && (
              <Descriptions.Item label="描述" span={2}>
                <Paragraph
                  ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
                >
                  {data.summary}
                </Paragraph>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>

        {/* 关联信息 */}
        <Card
          title={
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>关联信息 ({relations.length})</span>
              {loading && <Spin size="small" />}
            </div>
          }
          size="small"
          style={{ marginBottom: 16 }}
          styles={{
            body: {
              padding: '16px',
              maxHeight: '300px',
              overflowY: 'auto',
            },
          }}
        >
          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin />
              <div style={{ marginTop: 8 }}>加载关联信息中...</div>
            </div>
          ) : relations.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无关联信息"
            />
          ) : (
            <>
              <Collapse ghost>
                {displayRelations.map((group, index) => (
                  <Panel
                    header={
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 8,
                        }}
                      >
                        <Tag color="blue">{group.relationName}</Tag>
                        <Text type="secondary">
                          ({group.relations.length} 个关联)
                        </Text>
                      </div>
                    }
                    key={index}
                  >
                    {group.relations.map((relation, relIndex) => {
                      // 确定显示的目标要素
                      const isSource = relation.sourceId === data.id;
                      const targetElement = isSource
                        ? relation.targetElement
                        : relation.sourceElement;
                      const targetType = isSource
                        ? relation.targetEntityType
                        : relation.sourceType;

                      return (
                        <div
                          key={relIndex}
                          style={{
                            padding: '12px',
                            border: '1px solid #f0f0f0',
                            borderRadius: '6px',
                            marginBottom: '8px',
                            backgroundColor: '#fafafa',
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 8,
                              marginBottom: 8,
                            }}
                          >
                            <Tag color={getTypeColor(targetType)}>
                              {getTypeName(targetType)}
                            </Tag>
                            <Text strong>
                              {targetElement?.name || '未知要素'}
                            </Text>
                            {relation.direction && (
                              <Tag color="orange">{relation.direction}</Tag>
                            )}
                          </div>

                          {relation.term && (
                            <div style={{ marginBottom: 4 }}>
                              <Text type="secondary">词条：</Text>
                              <Text>{relation.term}</Text>
                            </div>
                          )}

                          {relation.record && (
                            <div>
                              <Text type="secondary">记载：</Text>
                              <Paragraph
                                style={{ margin: 0 }}
                                ellipsis={{
                                  rows: 2,
                                  expandable: true,
                                  symbol: '展开',
                                }}
                              >
                                {relation.record}
                              </Paragraph>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </Panel>
                ))}
              </Collapse>

              {relationGroups.length > 3 && (
                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <Button
                    type="link"
                    onClick={() => setShowAllRelations(!showAllRelations)}
                  >
                    {showAllRelations
                      ? '收起'
                      : `查看更多 (${relationGroups.length - 3} 个关系类型)`}
                  </Button>
                </div>
              )}
            </>
          )}
        </Card>

        {/* 关系网络图 */}
        {networkData && networkData.nodes.length > 1 && (
          <Card title="关系网络图" size="small" className="network-graph-card">
            <div style={{ height: '400px' }}>
              <NetworkGraph
                data={networkData}
                loading={loading}
                title=""
                height="100%"
                onNodeClick={(nodeData) => {
                  console.log('节点点击:', nodeData);
                  // 可以在这里实现节点点击跳转到对应要素详情
                }}
              />
            </div>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default MapDetailModal;
