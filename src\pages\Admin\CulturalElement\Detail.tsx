import DetailLayout from '@/components/DetailLayout';
import PhotoGallery from '@/components/PhotoGallery';
import RelationshipGraph from '@/components/RelationshipGraph';
import { getCulturalElementDetail, getCulturalElementPhotos } from '@/services/culturalElement';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Paragraph } = Typography;

const CulturalElementDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<API.CulturalElement | null>(null);
  const [photos, setPhotos] = useState<API.ElementPhoto[]>([]);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [loading, setLoading] = useState(true);
  const [photosLoading, setPhotosLoading] = useState(false);
  const [relationsLoading, setRelationsLoading] = useState(false);

  // 获取详情数据
  const fetchDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await getCulturalElementDetail(Number(id));
      
      if (response.errCode === 0 && response.data) {
        setData(response.data);
      } else {
        message.error(response.msg || '获取详情失败');
        history.goBack();
      }
    } catch (error: any) {
      console.error('获取文化要素详情失败:', error);
      message.error(error?.message || '获取详情失败');
      history.goBack();
    } finally {
      setLoading(false);
    }
  };

  // 获取照片数据
  const fetchPhotos = async () => {
    if (!id) return;
    
    setPhotosLoading(true);
    try {
      const response = await getCulturalElementPhotos(Number(id));
      
      if (response.errCode === 0 && response.data) {
        setPhotos(response.data);
      } else {
        console.warn('获取照片失败:', response.msg);
        setPhotos([]);
      }
    } catch (error: any) {
      console.error('获取照片失败:', error);
      setPhotos([]);
    } finally {
      setPhotosLoading(false);
    }
  };

  // 获取关系数据
  const fetchRelations = async () => {
    if (!id) return;
    
    setRelationsLoading(true);
    try {
      const response = await getPublicElementRelationsByElement(Number(id));
      
      if (response.errCode === 0 && response.data) {
        setRelations(response.data);
      } else {
        console.warn('获取关系数据失败:', response.msg);
        setRelations([]);
      }
    } catch (error: any) {
      console.error('获取关系数据失败:', error);
      setRelations([]);
    } finally {
      setRelationsLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchDetail();
    fetchPhotos();
    fetchRelations();
  }, [id]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <DetailLayout
      title={data.name}
      subtitle={`编号：${data.code || '-'}`}
      breadcrumb={[
        { title: '管理后台', path: '/admin' },
        { title: '文化要素管理', path: '/admin/cultural-element' },
        { title: '详情' },
      ]}
      actions={
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => history.goBack()}
          >
            返回
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => history.push(`/admin/cultural-element/edit/${id}`)}
          >
            编辑
          </Button>
        </Space>
      }
    >
      <Row gutter={[16, 16]}>
        {/* 基本信息 */}
        <Col xs={24} lg={12}>
          <Card title="基本信息" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="名称">
                <Title level={5} style={{ margin: 0 }}>
                  {data.name}
                </Title>
              </Descriptions.Item>
              <Descriptions.Item label="编号">
                {data.code || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                <Tag color="blue">
                  {data.typeName || data.typeDict?.typeName || '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="区域">
                <Tag color="green">
                  {data.regionName || data.regionDict?.regionName || '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="古城">
                <Tag color="orange">
                  {data.ancientCityName || data.ancientCity?.name || '-'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="建造年份">
                {data.constructionYear || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 位置信息 */}
        <Col xs={24} lg={12}>
          <Card title="位置信息" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="经度">
                {data.longitude?.toFixed(6) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="纬度">
                {data.latitude?.toFixed(6) || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="坐标">
                {data.longitude && data.latitude
                  ? `${data.longitude.toFixed(4)}, ${data.latitude.toFixed(4)}`
                  : '-'
                }
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 描述信息 */}
        {data.description && (
          <Col xs={24}>
            <Card title="描述信息" size="small">
              <Paragraph>{data.description}</Paragraph>
            </Card>
          </Col>
        )}

        {/* 照片展示 */}
        <Col xs={24}>
          <Card title="相关照片" size="small">
            <PhotoGallery
              photos={photos}
              loading={photosLoading}
              entityType="culturalElement"
              entityId={Number(id)}
              onUpdate={fetchPhotos}
            />
          </Card>
        </Col>

        {/* 关系图谱 */}
        <Col xs={24}>
          <Card title="关系图谱" size="small">
            <RelationshipGraph
              relations={relations}
              loading={relationsLoading}
              centerNodeId={Number(id)}
              centerNodeName={data.name}
              centerNodeType="culturalElement"
            />
          </Card>
        </Col>

        {/* 系统信息 */}
        <Col xs={24}>
          <Card title="系统信息" size="small">
            <Descriptions column={2} size="small">
              <Descriptions.Item label="创建时间">
                {data.createdAt ? new Date(data.createdAt).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {data.updatedAt ? new Date(data.updatedAt).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建者">
                {data.creator?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新者">
                {data.updater?.name || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </DetailLayout>
  );
};

export default CulturalElementDetail;
