// 地图标记点配置

// 地图标记点类型定义
export interface MapMarkerItem {
  id: number;
  type: 'mountain' | 'waterSystem' | 'historicalElement';
  name: string;
  longitude: number;
  latitude: number;
  elevation?: number;
  summary?: string;
  thumbnailUrl?: string;
  photos?: Array<{
    id: number;
    name: string;
    url: string;
  }>;
}

// 根据类型获取标记点图标
export const getMarkerIcon = (type: MapMarkerItem['type']) => {
  const iconMap = {
    mountain: {
      image: '/images/markers/mountain.png', // 山塬图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
    waterSystem: {
      image: '/images/markers/water.png', // 水系图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
    historicalElement: {
      image: '/images/markers/historical.png', // 历史要素图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
  };
  return iconMap[type];
};

// 根据类型获取标记点颜色
export const getMarkerColor = (type: MapMarkerItem['type']) => {
  const colorMap = {
    mountain: '#FF4444', // 红色 - 山塬
    waterSystem: '#4285F4', // 蓝色 - 水系
    historicalElement: '#34A853', // 绿色 - 历史要素
  };
  return colorMap[type];
};

// 根据类型获取中文名称
export const getTypeLabel = (type: MapMarkerItem['type']) => {
  const labelMap = {
    mountain: '山塬',
    waterSystem: '水系',
    historicalElement: '历史要素',
  };
  return labelMap[type];
};

// ==================== 新增：基于typeDictId的类型识别 ====================

/**
 * 类型字典ID映射（与适配器保持一致）
 */
export const TYPE_DICT_MAPPING = {
  HISTORICAL_ELEMENTS: [1, 2, 3, 4, 5],
  MOUNTAINS: [6, 7, 8],
  WATER_SYSTEMS: [9, 10, 11],
} as const;

/**
 * 根据typeDictId获取旧版本类型
 */
export const getTypeFromTypeDictId = (typeDictId?: number): MapMarkerItem['type'] => {
  if (!typeDictId) return 'historicalElement'; // 默认为历史要素

  if (TYPE_DICT_MAPPING.MOUNTAINS.includes(typeDictId)) {
    return 'mountain';
  }
  if (TYPE_DICT_MAPPING.WATER_SYSTEMS.includes(typeDictId)) {
    return 'waterSystem';
  }
  if (TYPE_DICT_MAPPING.HISTORICAL_ELEMENTS.includes(typeDictId)) {
    return 'historicalElement';
  }

  return 'historicalElement'; // 默认为历史要素
};

/**
 * 根据类型名称获取旧版本类型（备用方案）
 */
export const getTypeFromTypeName = (typeName?: string): MapMarkerItem['type'] => {
  if (!typeName) return 'historicalElement';

  if (typeName.includes('山') || typeName.includes('塬')) {
    return 'mountain';
  }
  if (typeName.includes('水') || typeName.includes('河') || typeName.includes('川')) {
    return 'waterSystem';
  }

  return 'historicalElement';
};

/**
 * 智能类型识别：优先使用typeDictId，备用typeName
 */
export const getSmartType = (typeDictId?: number, typeName?: string): MapMarkerItem['type'] => {
  // 优先使用typeDictId进行判断
  if (typeDictId) {
    const typeFromId = getTypeFromTypeDictId(typeDictId);
    if (typeFromId !== 'historicalElement' || TYPE_DICT_MAPPING.HISTORICAL_ELEMENTS.includes(typeDictId)) {
      return typeFromId;
    }
  }

  // 备用方案：使用typeName进行判断
  return getTypeFromTypeName(typeName);
};

/**
 * 根据统一的文化要素数据获取标记点图标
 */
export const getMarkerIconFromCulturalElement = (element: {
  typeDictId?: number;
  typeName?: string;
}) => {
  const type = getSmartType(element.typeDictId, element.typeName);
  return getMarkerIcon(type);
};

/**
 * 根据统一的文化要素数据获取标记点颜色
 */
export const getMarkerColorFromCulturalElement = (element: {
  typeDictId?: number;
  typeName?: string;
}) => {
  const type = getSmartType(element.typeDictId, element.typeName);
  return getMarkerColor(type);
};

/**
 * 根据统一的文化要素数据获取类型标签
 */
export const getTypeLabelFromCulturalElement = (element: {
  typeDictId?: number;
  typeName?: string;
}) => {
  const type = getSmartType(element.typeDictId, element.typeName);
  return getTypeLabel(type);
};
