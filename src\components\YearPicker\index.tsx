import { DatePicker } from 'antd';
import type { DatePickerProps } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

export interface YearPickerProps extends Omit<DatePickerProps, 'picker' | 'value' | 'onChange'> {
  value?: number;
  onChange?: (year: number | undefined) => void;
}

const YearPicker: React.FC<YearPickerProps> = ({
  value,
  onChange,
  ...props
}) => {
  const handleChange = (date: any) => {
    if (date) {
      onChange?.(date.year());
    } else {
      onChange?.(undefined);
    }
  };

  return (
    <DatePicker
      {...props}
      picker="year"
      value={value ? dayjs(value.toString()) : undefined}
      onChange={handleChange}
    />
  );
};

export default YearPicker;
