.cultural-element-management {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header-card {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-content {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
        }
        
        .page-description {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
      
      .statistics-info {
        display: flex;
        gap: 24px;
        
        .stat-item {
          text-align: center;
          
          .stat-value {
            display: block;
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            line-height: 1;
          }
          
          .stat-label {
            display: block;
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .search-card,
  .ant-card {
    .ant-card-body {
      padding: 20px;
    }
  }

  .search-card {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 768px) {
  .cultural-element-management {
    padding: 8px;
    
    .page-header-card .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .statistics-info {
        gap: 16px;
        
        .stat-item .stat-value {
          font-size: 20px;
        }
      }
    }
    
    .search-card .ant-card-body {
      padding: 16px;
    }
  }
}
