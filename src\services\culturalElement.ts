import { request } from '@umijs/max';

// ==================== 管理端文化要素接口 ====================

/**
 * 创建文化要素
 */
export async function createCulturalElement(
  params: API.CreateCulturalElementParams,
): Promise<API.ResType<API.CulturalElement>> {
  return request('/admin/cultural-element', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新文化要素
 */
export async function updateCulturalElement(
  id: number,
  params: API.UpdateCulturalElementParams,
): Promise<API.ResType<API.CulturalElement>> {
  return request(`/admin/cultural-element/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除文化要素
 */
export async function deleteCulturalElement(
  id: number,
): Promise<API.ResType<API.MessageResponse>> {
  return request(`/admin/cultural-element/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取文化要素详情
 */
export async function getCulturalElementDetail(
  id: number,
): Promise<API.ResType<API.CulturalElement>> {
  return request(`/admin/cultural-element/${id}`, {
    method: 'GET',
  });
}

/**
 * 分页查询文化要素
 */
export async function getCulturalElementList(
  params?: API.CulturalElementQueryDTO,
): Promise<API.ResType<API.CulturalElementListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
  if (params?.name) queryParams.append('name', params.name);
  if (params?.code) queryParams.append('code', params.code);
  if (params?.typeDictId) queryParams.append('typeDictId', params.typeDictId.toString());
  if (params?.regionDictId) queryParams.append('regionDictId', params.regionDictId.toString());
  if (params?.ancientCityId) queryParams.append('ancientCityId', params.ancientCityId.toString());
  if (params?.constructionYear) queryParams.append('constructionYear', params.constructionYear.toString());
  if (params?.keyword) queryParams.append('keyword', params.keyword);

  const url = `/admin/cultural-element${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 按类型查询文化要素
 */
export async function getCulturalElementsByType(
  typeDictId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/admin/cultural-element/type/${typeDictId}`, {
    method: 'GET',
  });
}

/**
 * 按区域查询文化要素
 */
export async function getCulturalElementsByRegion(
  regionDictId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/admin/cultural-element/region/${regionDictId}`, {
    method: 'GET',
  });
}

/**
 * 按古城查询文化要素
 */
export async function getCulturalElementsByAncientCity(
  ancientCityId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/admin/cultural-element/ancient-city/${ancientCityId}`, {
    method: 'GET',
  });
}

/**
 * 获取文化要素统计信息
 */
export async function getCulturalElementStatistics(
  params?: { regionDictId?: number; typeDictId?: number },
): Promise<API.ResType<API.CulturalElementStatistics>> {
  const queryParams = new URLSearchParams();
  if (params?.regionDictId) queryParams.append('regionDictId', params.regionDictId.toString());
  if (params?.typeDictId) queryParams.append('typeDictId', params.typeDictId.toString());

  const url = `/admin/cultural-element/statistics${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 批量创建文化要素
 */
export async function batchCreateCulturalElements(
  params: API.BatchCreateCulturalElementDTO,
): Promise<API.ResType<{ successCount: number; failureCount: number; errors?: any[] }>> {
  return request('/admin/cultural-element/batch', {
    method: 'POST',
    data: params,
  });
}

/**
 * 导出文化要素Excel
 */
export async function exportCulturalElements(
  params?: API.CulturalElementQueryDTO,
): Promise<API.ResType<{ downloadUrl: string; filename: string }>> {
  return request('/admin/cultural-element/export', {
    method: 'POST',
    data: params,
  });
}

/**
 * 下载导入模板
 */
export async function getCulturalElementImportTemplate(): Promise<API.ResType<API.TemplateDownloadResponse>> {
  return request('/admin/cultural-element/template', {
    method: 'GET',
  });
}

/**
 * 预览导入数据
 */
export async function previewCulturalElementImport(
  file: File,
): Promise<API.ResType<API.CulturalElementImportPreviewResponse>> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/admin/cultural-element/import/preview', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 执行导入
 */
export async function executeCulturalElementImport(
  file: File,
): Promise<API.ResType<API.CulturalElementImportResponse>> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/admin/cultural-element/import/execute', {
    method: 'POST',
    data: formData,
  });
}

// ==================== 公开文化要素接口 ====================

/**
 * 获取公开文化要素详情
 */
export async function getPublicCulturalElementDetail(
  id: number,
): Promise<API.ResType<API.CulturalElement>> {
  return request(`/api/cultural-element/${id}`, {
    method: 'GET',
  });
}

/**
 * 分页查询公开文化要素
 */
export async function getPublicCulturalElementList(
  params?: API.CulturalElementQueryDTO,
): Promise<API.ResType<API.CulturalElementListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
  if (params?.name) queryParams.append('name', params.name);
  if (params?.code) queryParams.append('code', params.code);
  if (params?.typeDictId) queryParams.append('typeDictId', params.typeDictId.toString());
  if (params?.regionDictId) queryParams.append('regionDictId', params.regionDictId.toString());
  if (params?.ancientCityId) queryParams.append('ancientCityId', params.ancientCityId.toString());
  if (params?.constructionYear) queryParams.append('constructionYear', params.constructionYear.toString());

  const url = `/api/cultural-element${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 按类型查询公开文化要素
 */
export async function getPublicCulturalElementsByType(
  typeDictId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/api/cultural-element/type/${typeDictId}`, {
    method: 'GET',
  });
}

/**
 * 按区域查询公开文化要素
 */
export async function getPublicCulturalElementsByRegion(
  regionDictId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/api/cultural-element/region/${regionDictId}`, {
    method: 'GET',
  });
}

/**
 * 按古城查询公开文化要素
 */
export async function getPublicCulturalElementsByAncientCity(
  ancientCityId: number,
): Promise<API.ResType<API.CulturalElement[]>> {
  return request(`/api/cultural-element/ancient-city/${ancientCityId}`, {
    method: 'GET',
  });
}

/**
 * 获取公开文化要素统计信息
 */
export async function getPublicCulturalElementStatistics(): Promise<API.ResType<API.CulturalElementStatistics>> {
  return request('/api/cultural-element/statistics', {
    method: 'GET',
  });
}

/**
 * 按类型分组获取文化要素
 */
export async function getPublicCulturalElementsGroupedByType(): Promise<API.ResType<API.CulturalElementGroupedData>> {
  return request('/api/cultural-element/grouped-by-type', {
    method: 'GET',
  });
}

/**
 * 按区域分组获取文化要素
 */
export async function getPublicCulturalElementsGroupedByRegion(): Promise<API.ResType<API.CulturalElementGroupedData>> {
  return request('/api/cultural-element/grouped-by-region', {
    method: 'GET',
  });
}

/**
 * 按古城分组获取文化要素
 */
export async function getPublicCulturalElementsGroupedByAncientCity(): Promise<API.ResType<API.CulturalElementGroupedData>> {
  return request('/api/cultural-element/grouped-by-ancient-city', {
    method: 'GET',
  });
}

/**
 * 根据编号查询文化要素
 */
export async function getPublicCulturalElementByCode(
  code: string,
): Promise<API.ResType<API.CulturalElement>> {
  return request(`/api/cultural-element/code/${code}`, {
    method: 'GET',
  });
}

/**
 * 获取所有文化要素
 */
export async function getAllPublicCulturalElements(): Promise<API.ResType<API.CulturalElement[]>> {
  return request('/api/cultural-element/all', {
    method: 'GET',
  });
}

/**
 * 搜索文化要素
 */
export async function searchPublicCulturalElements(
  params?: API.SearchCulturalElementParams,
): Promise<API.ResType<API.CulturalElementListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.keyword) queryParams.append('keyword', params.keyword);
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());

  const url = `/api/cultural-element/search${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取热门文化要素
 */
export async function getPopularCulturalElements(
  params?: API.PopularCulturalElementParams,
): Promise<API.ResType<API.CulturalElement[]>> {
  const queryParams = new URLSearchParams();
  if (params?.limit) queryParams.append('limit', params.limit.toString());

  const url = `/api/cultural-element/popular${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取推荐文化要素
 */
export async function getRecommendedCulturalElements(
  params?: API.PopularCulturalElementParams,
): Promise<API.ResType<API.CulturalElement[]>> {
  const queryParams = new URLSearchParams();
  if (params?.limit) queryParams.append('limit', params.limit.toString());

  const url = `/api/cultural-element/recommended${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

// ==================== 统一地图接口 ====================

/**
 * 获取统一的文化要素地图数据（新版本）
 */
export async function getCulturalElementMapData(
  params?: API.MapDataQueryDTO,
): Promise<API.ResType<API.MapDataResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.typeDictId) queryParams.append('typeDictId', params.typeDictId.toString());
  if (params?.regionDictId) queryParams.append('regionDictId', params.regionDictId.toString());
  if (params?.ancientCityId) queryParams.append('ancientCityId', params.ancientCityId.toString());
  if (params?.constructionYear) queryParams.append('constructionYear', params.constructionYear.toString());

  const url = `/openapi/map/cultural-elements${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取传统分类地图数据（兼容旧版本）
 */
export async function getTraditionalMapData(
  params?: API.MapDataQueryDTO,
): Promise<API.ResType<API.MapDataResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.typeDictId) queryParams.append('typeDictId', params.typeDictId.toString());
  if (params?.regionDictId) queryParams.append('regionDictId', params.regionDictId.toString());
  if (params?.ancientCityId) queryParams.append('ancientCityId', params.ancientCityId.toString());
  if (params?.constructionYear) queryParams.append('constructionYear', params.constructionYear.toString());

  const url = `/openapi/map/data${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

// ==================== 文化要素照片相关接口 ====================

/**
 * 获取文化要素照片列表
 */
export async function getCulturalElementPhotos(
  id: number,
): Promise<API.ResType<Array<{ id: number; name: string; url: string }>>> {
  return request(`/api/cultural-element/${id}/photos`, {
    method: 'GET',
  });
}

/**
 * 获取公开文化要素照片列表
 */
export async function getPublicCulturalElementPhotos(
  id: number,
): Promise<API.ResType<Array<{ id: number; name: string; url: string }>>> {
  return request(`/openapi/cultural-element/${id}/photos`, {
    method: 'GET',
  });
}
