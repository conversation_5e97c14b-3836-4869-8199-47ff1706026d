import { EnvironmentOutlined } from '@ant-design/icons';
import { Button, Modal } from 'antd';
import React, { useState } from 'react';

export interface LocationPickerProps {
  longitude?: number;
  latitude?: number;
  onChange?: (longitude: number, latitude: number) => void;
  disabled?: boolean;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  longitude,
  latitude,
  onChange,
  disabled = false,
}) => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenMap = () => {
    setModalVisible(true);
  };

  const handleMapConfirm = (lng: number, lat: number) => {
    onChange?.(lng, lat);
    setModalVisible(false);
  };

  const hasLocation = longitude !== undefined && latitude !== undefined;

  return (
    <>
      <Button
        icon={<EnvironmentOutlined />}
        onClick={handleOpenMap}
        disabled={disabled}
        type={hasLocation ? 'primary' : 'default'}
      >
        {hasLocation ? '修改位置' : '选择位置'}
      </Button>

      <Modal
        title="选择位置"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={null}
      >
        <div style={{ height: 400, background: '#f0f0f0', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div style={{ textAlign: 'center', color: '#999' }}>
            <EnvironmentOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>地图组件占位</div>
            <div style={{ fontSize: 12, marginTop: 8 }}>
              当前位置: {hasLocation ? `${longitude?.toFixed(6)}, ${latitude?.toFixed(6)}` : '未选择'}
            </div>
            <Button
              type="primary"
              style={{ marginTop: 16 }}
              onClick={() => handleMapConfirm(108.9398, 34.3412)}
            >
              确认选择
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default LocationPicker;
