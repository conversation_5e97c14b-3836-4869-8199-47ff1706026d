# 前端文化要素统一接口迁移实施计划

## 概述

本文档详细描述了前端从分离的三张表（山塬、水系、历史要素）迁移到统一文化要素表的具体实施步骤。采用渐进式迁移策略，确保迁移过程中系统始终可用。

## 迁移策略

- **渐进式迁移**：分阶段实施，每个阶段都可独立验证
- **向后兼容**：迁移期间保持旧功能可用
- **适配器模式**：创建适配层实现新旧接口的平滑过渡

---

## 第一阶段：基础设施准备

### 步骤 1.1：新增文化要素类型定义

**目标**：在类型定义文件中添加新的文化要素相关类型

**文件**：`src/services/typings.d.ts`

**任务**：
1. 添加 `CulturalElement` 接口定义
2. 添加 `CulturalElementQueryDTO` 查询参数类型
3. 添加 `CulturalElementStatistics` 统计数据类型
4. 添加创建、更新、列表响应等相关类型
5. 添加地图数据相关类型

**验收标准**：
- 类型定义与后台文档完全一致
- 所有必要的接口和类型都已定义
- TypeScript 编译无错误

### 步骤 1.2：创建文化要素服务文件

**目标**：创建新的统一文化要素服务接口

**文件**：`src/services/culturalElement.ts`

**任务**：
1. 实现管理端 CRUD 操作接口
2. 实现公开查询接口
3. 实现统计和分组接口
4. 实现搜索和推荐接口
5. 实现批量操作接口
6. 实现地图数据接口

**验收标准**：
- 所有接口函数都已实现
- 接口路径与后台文档一致
- 包含完整的错误处理
- 支持 TypeScript 类型检查

---

## 第二阶段：适配层创建

### 步骤 2.1：创建数据适配器

**目标**：创建适配器实现新旧接口的转换

**文件**：`src/utils/culturalElementAdapter.ts`

**任务**：
1. 定义类型映射常量（山塬、水系、历史要素对应的 typeDictId）
2. 实现旧接口到新接口的适配函数
3. 实现数据格式转换函数
4. 实现按类型分组的数据处理函数
5. 提供向后兼容的查询参数转换

**验收标准**：
- 适配器函数能正确转换查询参数
- 数据格式转换准确无误
- 支持所有现有的查询场景

### 步骤 2.2：更新地图配置

**目标**：更新地图相关的类型处理逻辑

**文件**：`src/services/mapConfig.ts`

**任务**：
1. 保持现有的图标和颜色配置
2. 更新类型判断逻辑以支持统一的文化要素
3. 添加基于 typeDictId 的类型识别函数
4. 确保地图标记点渲染逻辑兼容新数据结构

**验收标准**：
- 地图标记点显示正常
- 不同类型的要素使用正确的图标和颜色
- 兼容新的数据结构

---

## 第三阶段：公开页面更新

### 步骤 3.1：更新山塬页面

**目标**：将山塬页面迁移到新接口

**文件**：`src/pages/Mountain/index.tsx`

**任务**：
1. 引入文化要素服务和适配器
2. 修改数据获取逻辑使用适配器函数
3. 保持现有的 UI 和交互逻辑不变
4. 确保分页、筛选、搜索功能正常
5. 更新图片获取逻辑

**验收标准**：
- 页面功能与原来完全一致
- 数据显示正确
- 所有交互功能正常

### 步骤 3.2：更新水系页面

**目标**：将水系页面迁移到新接口

**文件**：`src/pages/WaterSystem/index.tsx`

**任务**：
1. 引入文化要素服务和适配器
2. 修改数据获取逻辑使用适配器函数
3. 保持现有的 UI 和交互逻辑不变
4. 确保分页、筛选、搜索功能正常
5. 更新图片获取逻辑

**验收标准**：
- 页面功能与原来完全一致
- 数据显示正确
- 所有交互功能正常

### 步骤 3.3：更新历史要素页面

**目标**：将历史要素页面迁移到新接口

**文件**：`src/pages/HistoricalElement/index.tsx`

**任务**：
1. 引入文化要素服务和适配器
2. 修改数据获取逻辑使用适配器函数
3. 保持现有的 UI 和交互逻辑不变
4. 确保分页、筛选、搜索功能正常
5. 更新图片获取逻辑

**验收标准**：
- 页面功能与原来完全一致
- 数据显示正确
- 所有交互功能正常

### 步骤 3.4：更新详情页面

**目标**：将详情页面迁移到新的统一接口

**文件**：`src/pages/Detail/index.tsx`

**任务**：
1. 修改详情数据获取逻辑
2. 使用统一的文化要素详情接口
3. 更新图片获取逻辑
4. 保持现有的 UI 展示效果
5. 确保关联关系显示正常

**验收标准**：
- 详情页面显示完整准确
- 图片加载正常
- 关联关系功能正常

---

## 第四阶段：首页地图更新

### 步骤 4.1：更新首页地图数据获取

**目标**：将首页地图迁移到新的统一地图接口

**文件**：`src/pages/Home/index.tsx`

**任务**：
1. 修改地图数据获取逻辑
2. 使用新的统一地图接口 `/openapi/map/cultural-elements`
3. 更新数据处理和标记点生成逻辑
4. 保持现有的地图交互功能
5. 确保概览统计数据正确

**验收标准**：
- 地图标记点显示正确
- 不同类型要素使用正确图标
- 地图交互功能正常
- 概览数据统计准确

---

## 第五阶段：数字化统计页面更新

### 步骤 5.1：更新数字化统计页面

**目标**：将统计页面迁移到新的统计接口

**文件**：`src/pages/Digital/index.tsx`

**任务**：
1. 修改统计数据获取逻辑
2. 使用新的统一统计接口
3. 适配新的数据结构和统计维度
4. 保持现有的图表展示效果
5. 更新时间轴和区域分布统计

**验收标准**：
- 所有统计图表显示正确
- 数据维度和分组准确
- 图表交互功能正常

---

## 第六阶段：管理端页面创建

### 步骤 6.1：创建统一文化要素管理页面

**目标**：创建新的统一文化要素管理功能

**文件**：`src/pages/Admin/CulturalElement/`

**任务**：
1. 创建页面目录结构
2. 实现列表页面（包含筛选、搜索、分页）
3. 实现新增/编辑表单页面
4. 实现批量操作功能
5. 实现导入导出功能
6. 创建相关的 hooks 和工具函数

**验收标准**：
- 完整的 CRUD 功能
- 批量操作正常
- 导入导出功能正常
- UI 与现有管理页面风格一致

### 步骤 6.2：更新路由配置

**目标**：添加新的文化要素管理路由

**文件**：`config/routes.ts`

**任务**：
1. 添加新的文化要素管理路由
2. 保持旧路由的兼容性（暂时）
3. 配置正确的权限控制
4. 更新菜单图标和名称

**验收标准**：
- 新路由可正常访问
- 权限控制正确
- 菜单显示正常

---

## 第七阶段：测试和验证

### 步骤 7.1：功能测试

**目标**：全面测试迁移后的功能

**任务**：
1. 测试所有公开页面功能
2. 测试管理端功能
3. 测试地图功能
4. 测试统计功能
5. 测试数据一致性

**验收标准**：
- 所有功能正常运行
- 数据显示准确
- 性能无明显下降

### 步骤 7.2：兼容性验证

**目标**：验证新旧接口并存期间的兼容性

**任务**：
1. 验证新旧数据的一致性
2. 测试错误处理机制
3. 验证缓存策略
4. 性能对比测试

**验收标准**：
- 新旧接口数据一致
- 错误处理正确
- 性能满足要求

---

## 第八阶段：清理和优化（后期）

### 步骤 8.1：清理旧代码

**目标**：移除不再需要的旧代码

**任务**：
1. 移除旧的服务文件
2. 清理旧的管理端页面
3. 更新路由配置
4. 清理无用的类型定义

**验收标准**：
- 代码库整洁
- 无死代码
- 构建正常

---

## 实施注意事项

1. **每个步骤完成后都要进行测试验证**
2. **保持代码提交的原子性，便于回滚**
3. **及时更新文档和注释**
4. **与后端团队保持沟通，确认接口变更**
5. **关注性能影响，必要时进行优化**

## 风险控制

1. **备份策略**：每个阶段开始前创建代码备份
2. **回滚计划**：准备快速回滚方案
3. **监控机制**：监控接口调用和错误率
4. **用户通知**：必要时通知用户系统维护

---

**文档版本**: v1.0  
**创建时间**: 2025-10-09  
**状态**: 待实施
