import { Empty, Spin } from 'antd';
import React, { useEffect, useRef } from 'react';
import './index.less';

export interface RelationshipGraphProps {
  relations: API.ElementRelation[];
  loading?: boolean;
  centerNodeId: number;
  centerNodeName: string;
  centerNodeType: string;
  height?: number;
}

const RelationshipGraph: React.FC<RelationshipGraphProps> = ({
  relations,
  loading = false,
  centerNodeId,
  centerNodeName,
  centerNodeType,
  height = 400,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || loading || relations.length === 0) {
      return;
    }

    // 这里应该使用图形库（如 D3.js, ECharts, G6 等）来渲染关系图
    // 由于没有具体的图形库依赖，这里只是一个占位实现
    const container = containerRef.current;
    container.innerHTML = `
      <div class="relationship-graph-placeholder">
        <div class="center-node">
          <div class="node-icon ${centerNodeType}"></div>
          <div class="node-name">${centerNodeName}</div>
        </div>
        <div class="relations-info">
          <p>关联关系数量: ${relations.length}</p>
          <ul>
            ${relations.slice(0, 5).map(relation => `
              <li>
                ${relation.relationName}: ${relation.targetElementName || relation.sourceElementName}
              </li>
            `).join('')}
            ${relations.length > 5 ? '<li>...</li>' : ''}
          </ul>
        </div>
      </div>
    `;
  }, [relations, loading, centerNodeId, centerNodeName, centerNodeType]);

  if (loading) {
    return (
      <div className="relationship-graph-loading" style={{ height }}>
        <Spin size="large" />
      </div>
    );
  }

  if (relations.length === 0) {
    return (
      <div className="relationship-graph-empty" style={{ height }}>
        <Empty description="暂无关联关系" />
      </div>
    );
  }

  return (
    <div className="relationship-graph">
      <div
        ref={containerRef}
        className="graph-container"
        style={{ height }}
      />
    </div>
  );
};

export default RelationshipGraph;
