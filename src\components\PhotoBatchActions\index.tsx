import { DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Modal, Space, message } from 'antd';
import React, { useState } from 'react';

export interface PhotoBatchActionsProps {
  selectedIds: number[];
  entityType: string;
  onSuccess?: () => void;
}

const PhotoBatchActions: React.FC<PhotoBatchActionsProps> = ({
  selectedIds,
  entityType,
  onSuccess,
}) => {
  const [loading, setLoading] = useState({
    delete: false,
    download: false,
  });

  // 批量删除照片
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      message.warning('请先选择要删除的项目');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedIds.length} 个项目的所有照片吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        setLoading(prev => ({ ...prev, delete: true }));
        try {
          // 这里应该调用批量删除照片的API
          // await batchDeleteElementPhotos(entityType, selectedIds);
          message.success(`成功删除 ${selectedIds.length} 个项目的照片`);
          onSuccess?.();
        } catch (error: any) {
          console.error('批量删除照片失败:', error);
          message.error(error?.message || '批量删除失败');
        } finally {
          setLoading(prev => ({ ...prev, delete: false }));
        }
      },
    });
  };

  // 批量下载照片
  const handleBatchDownload = async () => {
    if (selectedIds.length === 0) {
      message.warning('请先选择要下载的项目');
      return;
    }

    setLoading(prev => ({ ...prev, download: true }));
    try {
      // 这里应该调用批量下载照片的API
      // await batchDownloadElementPhotos(entityType, selectedIds);
      message.success('照片打包下载已开始');
    } catch (error: any) {
      console.error('批量下载照片失败:', error);
      message.error(error?.message || '批量下载失败');
    } finally {
      setLoading(prev => ({ ...prev, download: false }));
    }
  };

  // 批量预览照片
  const handleBatchPreview = () => {
    if (selectedIds.length === 0) {
      message.warning('请先选择要预览的项目');
      return;
    }

    // 这里可以打开一个照片预览的模态框
    message.info('照片预览功能开发中');
  };

  if (selectedIds.length === 0) {
    return null;
  }

  return (
    <div style={{ marginTop: 16, padding: 16, background: '#f5f5f5', borderRadius: 8 }}>
      <Space>
        <span>已选择 {selectedIds.length} 个项目</span>
        <Button
          icon={<EyeOutlined />}
          onClick={handleBatchPreview}
        >
          批量预览照片
        </Button>
        <Button
          icon={<DownloadOutlined />}
          loading={loading.download}
          onClick={handleBatchDownload}
        >
          批量下载照片
        </Button>
        <Button
          danger
          icon={<DeleteOutlined />}
          loading={loading.delete}
          onClick={handleBatchDelete}
        >
          批量删除照片
        </Button>
      </Space>
    </div>
  );
};

export default PhotoBatchActions;
