import NetworkGraph from '@/components/NetworkGraph';
import { Card, Empty } from 'antd';
import React, { useMemo } from 'react';

export interface ElementNetworkGraphProps {
  /** 关联关系数据 */
  relations: API.ElementRelation[];
  /** 当前要素数据 */
  currentElement: {
    id: number;
    name: string;
    type: string;
  };
  /** 加载状态 */
  loading?: boolean;
  /** 图表高度 */
  height?: number | string;
  /** 图表标题 */
  title?: string;
  /** 节点点击回调 */
  onNodeClick?: (nodeData: any) => void;
  /** 连线点击回调 */
  onLinkClick?: (linkData: any) => void;
  /** 样式 */
  style?: React.CSSProperties;
  /** 类名 */
  className?: string;
}

const ElementNetworkGraph: React.FC<ElementNetworkGraphProps> = ({
  relations,
  currentElement,
  loading = false,
  height = 400,
  title = '关系网络图',
  onNodeClick,
  onLinkClick,
  style,
  className,
}) => {
  // 获取节点颜色
  const getNodeColor = (nodeType: string): string => {
    const colorMap: Record<string, string> = {
      mountain: '#8B4513', // 棕色 - 山塬
      water_system: '#4169E1', // 蓝色 - 水系
      waterSystem: '#4169E1', // 蓝色 - 水系（兼容）
      historical_element: '#DC143C', // 红色 - 历史要素
      historicalElement: '#DC143C', // 红色 - 历史要素（兼容）
      type_dict: '#32CD32', // 绿色 - 类型
      region_dict: '#FFD700', // 金色 - 区域
    };
    return colorMap[nodeType] || '#666';
  };

  // 获取连线颜色
  const getLinkColor = (relationName: string): string => {
    const colorMap: Record<string, string> = {
      选址关联: '#4169E1', // 蓝色
      视线关联: '#9932CC', // 紫色
      历史关联: '#DC143C', // 红色
      功能关联: '#32CD32', // 绿色
      其他关联: '#808080', // 灰色
    };
    return colorMap[relationName] || '#999';
  };

  // 获取目标要素的显示名称
  const getTargetElementName = (relation: API.ElementRelation) => {
    // 根据接口文档，优先使用 targetElement.name
    if (relation.targetElement) {
      return relation.targetElement.name;
    }
    // 如果没有 targetElement，使用 term 字段
    return relation.term || '未知要素';
  };

  // 获取目标要素类型的显示名称
  const getTargetElementTypeName = (relation: API.ElementRelation) => {
    // 根据接口文档，使用 targetEntityType 字段判断具体的要素类型
    switch (relation.targetEntityType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
      case 'waterSystem':
        return '水系';
      case 'historical_element':
      case 'historicalElement':
        return '历史要素';
      case 'type_dict':
        return '类型';
      case 'region_dict':
        return '区域';
      default:
        return '其他';
    }
  };

  // 获取源要素类型的显示名称
  const getSourceElementTypeName = (sourceType: string) => {
    switch (sourceType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
      case 'waterSystem':
        return '水系';
      case 'historical_element':
      case 'historicalElement':
        return '历史要素';
      default:
        return '其他';
    }
  };

  // 获取当前要素类型的显示名称
  const getCurrentElementTypeName = (type: string) => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'water_system':
      case 'waterSystem':
        return '水系';
      case 'historical_element':
      case 'historicalElement':
        return '历史要素';
      default:
        return '其他';
    }
  };

  // 转换关联关系数据为网络图数据
  const networkData = useMemo((): API.NetworkGraphData | null => {
    if (!relations || relations.length === 0) {
      return null;
    }

    console.log('🔄 ElementNetworkGraph 开始转换网络图数据:', {
      relations,
      currentElement,
    });

    const nodes: API.NetworkGraphNode[] = [];
    const links: API.NetworkGraphLink[] = [];
    const nodeMap = new Map<string, API.NetworkGraphNode>();

    // 添加当前要素作为中心节点
    const currentNodeId = `${currentElement.type}_${currentElement.id}`;
    const currentNode: API.NetworkGraphNode = {
      id: currentNodeId,
      name: currentElement.name || '当前要素',
      type:
        currentElement.type === 'waterSystem'
          ? 'water_system'
          : currentElement.type === 'historicalElement'
          ? 'historical_element'
          : currentElement.type || 'unknown',
      category: getCurrentElementTypeName(currentElement.type),
      size: 20, // 中心节点较大
      color: getNodeColor(currentElement.type),
    };
    console.log('🎯 中心节点:', currentNode);
    nodeMap.set(currentNodeId, currentNode);

    // 处理关联关系
    console.log('🔗 处理关联关系，数量:', relations.length);
    relations.forEach((relation, index) => {
      console.log(`🔗 处理第${index + 1}个关系:`, relation);

      // 添加源要素节点
      const sourceNodeId = `${relation.sourceType}_${relation.sourceId}`;
      if (!nodeMap.has(sourceNodeId)) {
        const sourceNode: API.NetworkGraphNode = {
          id: sourceNodeId,
          name: relation.sourceElement?.name || '未知要素',
          type:
            relation.sourceType === 'water_system'
              ? 'water_system'
              : relation.sourceType === 'historical_element'
              ? 'historical_element'
              : relation.sourceType || 'unknown',
          category: getSourceElementTypeName(relation.sourceType),
          size: 15,
          color: getNodeColor(relation.sourceType),
        };
        console.log('➕ 添加源节点:', sourceNode);
        nodeMap.set(sourceNodeId, sourceNode);
      }

      // 添加目标要素节点
      const targetNodeId = `${relation.targetEntityType}_${relation.targetId}`;
      if (!nodeMap.has(targetNodeId)) {
        const targetNode: API.NetworkGraphNode = {
          id: targetNodeId,
          name: getTargetElementName(relation),
          type: relation.targetEntityType || 'unknown',
          category: getTargetElementTypeName(relation),
          size: 15,
          color: getNodeColor(relation.targetEntityType || 'unknown'),
        };
        console.log('➕ 添加目标节点:', targetNode);
        nodeMap.set(targetNodeId, targetNode);
      }

      // 创建连线
      const link: API.NetworkGraphLink = {
        source: sourceNodeId,
        target: targetNodeId,
        relation: relation.relationDict?.relationName || '其他关系',
        direction: relation.direction,
        term: relation.term,
        weight: 1,
        color: getLinkColor(relation.relationDict?.relationName || '其他关系'),
      };
      console.log('🔗 添加连线:', link);
      links.push(link);
    });

    // 转换节点映射为数组
    nodes.push(...Array.from(nodeMap.values()));

    // 生成分类列表
    const categories = Array.from(new Set(nodes.map((node) => node.category)));

    const result = {
      nodes,
      links,
      categories,
    };

    console.log('✅ ElementNetworkGraph 网络图数据转换完成:', result);
    return result;
  }, [relations, currentElement]);

  // 渲染内容
  if (!networkData || networkData.nodes.length <= 1) {
    return (
      <Card title={title} style={style} className={className}>
        <Empty description="暂无关系数据" style={{ padding: '40px 0' }} />
      </Card>
    );
  }

  return (
    <Card title={title} style={style} className={className}>
      <div
        style={{ height: typeof height === 'number' ? `${height}px` : height }}
      >
        <NetworkGraph
          data={networkData}
          loading={loading}
          height="100%"
          onNodeClick={onNodeClick}
          onLinkClick={onLinkClick}
        />
      </div>
    </Card>
  );
};

export default ElementNetworkGraph;
