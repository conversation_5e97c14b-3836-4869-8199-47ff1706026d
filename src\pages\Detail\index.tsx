import ElementNetworkGraph from '@/components/ElementNetworkGraph';
import PublicLayout from '@/components/PublicLayout';
import type { DictionaryState } from '@/models/dictionary';
import type { RegionDict, TypeDict } from '@/pages/Admin/Dictionary/dict-types';
import { getPublicElementRelationsByElement } from '@/services/elementRelation';
import {
  getMountainDetailAdapter,
  getWaterSystemDetailAdapter,
  getHistoricalElementDetailAdapter,
  getElementPhotosAdapter,
} from '@/utils/culturalElementAdapter';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { connect, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Collapse,
  Empty,
  Image,
  message,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './styles.less';

interface DetailPageProps {
  regionList: RegionDict[];
  typeList: TypeDict[];
  dispatch: any;
}

const DetailPage: React.FC<DetailPageProps> = ({
  regionList,
  typeList,
  dispatch,
}) => {
  const { type, id } = useParams();
  const numericId = Number(id);

  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<
    Array<{ id: number; name: string; url: string }>
  >([]);
  const [relations, setRelations] = useState<API.ElementRelation[]>([]);
  const [relationsLoading, setRelationsLoading] = useState<boolean>(false);

  const getTypeName = () => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'waterSystem':
        return '水系';
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  // 递归查找字典项
  const findDictItem = (list: any[], id: number): any => {
    for (const item of list) {
      if (item.id === id) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = findDictItem(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 获取区域名称
  const getRegionName = (regionDictId?: number) => {
    if (!regionDictId || !regionList || regionList.length === 0) {
      return detail?.regionDict?.regionName || '-';
    }
    const regionItem = findDictItem(regionList, regionDictId);
    return regionItem?.regionName || detail?.regionDict?.regionName || '-';
  };

  // 获取类型名称
  const getTypeDictName = (typeDictId?: number) => {
    if (!typeDictId || !typeList || typeList.length === 0) {
      return detail?.typeDict?.typeName || '-';
    }
    const typeItem = findDictItem(typeList, typeDictId);
    return typeItem?.typeName || detail?.typeDict?.typeName || '-';
  };

  // 获取关联关系数据
  const loadRelations = async () => {
    if (!type || !numericId) return;

    setRelationsLoading(true);
    try {
      // 根据接口文档，关联关系接口的路径参数
      const elementType =
        type === 'waterSystem'
          ? 'waterSystem' // 接口文档中使用 waterSystem
          : type === 'historicalElement'
          ? 'historicalElement' // 接口文档中使用 historicalElement
          : type; // mountain 保持不变

      const response = await getPublicElementRelationsByElement(
        elementType,
        numericId,
      );
      if (response.errCode === 0) {
        const relationData = response.data || [];
        setRelations(relationData);

        // 关联关系数据已设置，网络图组件会自动处理
        console.log('🔗 关联关系数据:', relationData);
      }
    } catch (error: any) {
      console.error('加载关联关系失败:', error);
      message.error('加载关联关系失败');
    } finally {
      setRelationsLoading(false);
    }
  };

  // 按关系类型分组关联数据
  const getGroupedRelations = () => {
    const grouped: { [key: string]: API.ElementRelation[] } = {};
    relations.forEach((relation) => {
      const relationName = relation.relationDict?.relationName || '其他关系';
      if (!grouped[relationName]) {
        grouped[relationName] = [];
      }
      grouped[relationName].push(relation);
    });
    return grouped;
  };

  // 获取类型颜色
  const getTypeColor = (type?: string) => {
    switch (type) {
      case 'mountain':
        return 'volcano';
      case 'water_system':
      case 'waterSystem':
        return 'blue';
      case 'historical_element':
      case 'historicalElement':
        return 'purple';
      default:
        return 'default';
    }
  };

  // 获取类型名称
  const getTypeNameByType = (type?: string) => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'water_system':
      case 'waterSystem':
        return '水系';
      case 'historical_element':
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  // 按关系类型分组并转换为抽屉组件格式
  const getRelationGroups = () => {
    const grouped = getGroupedRelations();
    return Object.entries(grouped).map(([relationName, relationList]) => ({
      relationName,
      relationCode: relationName,
      relations: relationList,
    }));
  };

  // 显示的关系组（支持展开/收起）
  const relationGroups = getRelationGroups();

  // 初始化字典数据
  useEffect(() => {
    if (!regionList || regionList.length === 0) {
      dispatch({ type: 'dictionary/fetchRegionList' });
    }
    if (!typeList || typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
  }, [dispatch, regionList, typeList]);

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        let detailData = null;
        if (type === 'mountain') {
          // 使用山塬详情适配器
          const [d, p] = await Promise.all([
            getMountainDetailAdapter(numericId),
            getElementPhotosAdapter(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'waterSystem') {
          // 使用水系详情适配器
          const [d, p] = await Promise.all([
            getWaterSystemDetailAdapter(numericId),
            getElementPhotosAdapter(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'historicalElement') {
          // 使用历史要素详情适配器
          const [d, p] = await Promise.all([
            getHistoricalElementDetailAdapter(numericId),
            getElementPhotosAdapter(numericId),
          ]);
          if (d.errCode === 0) {
            detailData = d.data || null;
            setDetail(detailData);
          }
          if (p.errCode === 0) setPhotos(p.data || []);
        }

        // 在详情加载完成后加载关联关系
        if (detailData) {
          await loadRelations();
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [type, numericId]);

  // 获取banner图片
  const getBannerImage = () => {
    // 优先使用关联的图片
    if (photos && photos.length > 0) {
      return photos[0].url;
    }

    // 使用本地的默认背景图片
    const defaultImages = {
      mountain: '/images/banners/mountain.jpg',
      waterSystem: '/images/banners/water.jpg',
      historicalElement: '/images/banners/historical.jpg',
      default: '/images/banners/forest.jpg',
    };

    return (
      defaultImages[type as keyof typeof defaultImages] || defaultImages.default
    );
  };

  return (
    <div className="detail-page">
      <PublicLayout>
        {/* Banner区域 */}
        <div className="banner-section">
          <div
            className="banner-image"
            style={{
              backgroundImage: `url(${getBannerImage()})`,
            }}
          >
            <div className="banner-overlay">
              <div className="banner-content">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => window.history.back()}
                  className="back-button"
                >
                  返回
                </Button>

                <div className="banner-title-section">
                  <h1 className="page-title">
                    {detail?.name || getTypeName()}
                  </h1>
                  {detail?.code && (
                    <div className="page-code">
                      <span className="code-tag">{detail.code}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          className="content-card"
          style={{ padding: '32px', margin: '32px auto', maxWidth: '1400px' }}
        >
          <div className="detail-layout">
            <Card loading={loading} title="基本信息" className="detail-card">
              <div className="basic-info">
                <div className="info-item">
                  <span className="info-label">名称：</span>
                  <span className="info-value">{detail?.name || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">编码：</span>
                  <span className="info-value">{detail?.code || '-'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">所属区域：</span>
                  <span className="info-value">
                    {getRegionName(detail?.regionDictId)}
                  </span>
                </div>
                {(detail?.typeDictId || detail?.typeDict) && (
                  <div className="info-item">
                    <span className="info-label">所属类型：</span>
                    <span className="info-value">
                      {getTypeDictName(detail?.typeDictId)}
                    </span>
                  </div>
                )}

                {/* 山塬特有字段 */}
                {type === 'mountain' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">海拔高度：</span>
                      <span className="info-value">
                        {detail?.height ? `${detail.height}米` : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                  </>
                )}

                {/* 水系特有字段 */}
                {type === 'waterSystem' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">长度面积：</span>
                      <span className="info-value">
                        {detail?.lengthArea || '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">地理坐标：</span>
                      <span className="info-value">
                        {detail?.longitude && detail?.latitude
                          ? `${detail.longitude}, ${detail.latitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                  </>
                )}

                {/* 历史要素特有字段 */}
                {type === 'historicalElement' && (
                  <>
                    <div className="info-item">
                      <span className="info-label">建造坐标：</span>
                      <span className="info-value">
                        {detail?.constructionLongitude &&
                        detail?.constructionLatitude
                          ? `${detail.constructionLongitude}, ${detail.constructionLatitude}`
                          : '-'}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">位置描述：</span>
                      <span className="info-value">
                        {detail?.locationDescription || '-'}
                      </span>
                    </div>
                    {detail?.constructionYear && (
                      <div className="info-item">
                        <span className="info-label">建造年份：</span>
                        <span className="info-value">
                          {detail.constructionYear < 0
                            ? `公元前${Math.abs(detail.constructionYear)}年`
                            : `公元${detail.constructionYear}年`}
                        </span>
                      </div>
                    )}
                  </>
                )}

                {/* 历史记载 - 所有类型都有 */}
                {detail?.historicalRecords && (
                  <div className="info-item">
                    <span className="info-label">历史记载：</span>
                    <div className="info-value historical-records">
                      {detail.historicalRecords}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card
              loading={loading}
              title="相关图片"
              className="detail-card photos-section"
            >
              {photos?.length ? (
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: 12 }}
                >
                  {photos.map((p) => (
                    <Image
                      key={p.id}
                      src={p.url}
                      className="photo-item"
                      style={{
                        width: '100%',
                        maxHeight: 480,
                        objectFit: 'cover',
                        borderRadius: '6px',
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div
                  style={{
                    color: '#999',
                    textAlign: 'center',
                    padding: '40px 0',
                    background: '#fafafa',
                    borderRadius: '6px',
                  }}
                >
                  暂无图片
                </div>
              )}
            </Card>

            <Card
              title={
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <span>关联信息 ({relations.length})</span>
                  {relationsLoading && <Spin size="small" />}
                </div>
              }
              className="detail-card relation-info"
              styles={{ body: { padding: '16px' } }}
            >
              {relationsLoading ? (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Spin />
                  <div style={{ marginTop: 8 }}>加载关联信息中...</div>
                </div>
              ) : relations.length === 0 ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无关联信息"
                  className="empty-state"
                />
              ) : (
                <>
                  <Collapse ghost defaultActiveKey={[0, 1, 2, 3, 4]}>
                    {relationGroups.map((group, index) => (
                      <Collapse.Panel
                        header={
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 8,
                            }}
                          >
                            <Tag color="blue">{group.relationName}</Tag>
                            <Typography.Text type="secondary">
                              ({group.relations.length} 个关联)
                            </Typography.Text>
                          </div>
                        }
                        key={index}
                      >
                        {group.relations.map((relation, relIndex) => {
                          // 确定显示的目标要素
                          const isSource =
                            relation.sourceElement?.name === detail?.name;
                          const targetElement = isSource
                            ? relation.targetElement
                            : relation.sourceElement;
                          const targetType = isSource
                            ? relation.targetEntityType
                            : relation.sourceType;

                          return (
                            <div
                              key={relIndex}
                              style={{
                                padding: '12px',
                                border: '1px solid #f0f0f0',
                                borderRadius: '6px',
                                marginBottom: '8px',
                                backgroundColor: '#fafafa',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 8,
                                  marginBottom: 8,
                                }}
                              >
                                <Tag color={getTypeColor(targetType)}>
                                  {getTypeNameByType(targetType)}
                                </Tag>
                                <Typography.Text strong>
                                  {targetElement?.name || '未知要素'}
                                </Typography.Text>
                                {relation.direction && (
                                  <Tag color="orange">{relation.direction}</Tag>
                                )}
                              </div>

                              {relation.term && (
                                <div style={{ marginBottom: 4 }}>
                                  <Typography.Text type="secondary">
                                    词条：
                                  </Typography.Text>
                                  <Typography.Text>
                                    {relation.term}
                                  </Typography.Text>
                                </div>
                              )}

                              {relation.record && (
                                <div>
                                  <Typography.Text type="secondary">
                                    记载：
                                  </Typography.Text>
                                  <Typography.Paragraph
                                    style={{ margin: 0 }}
                                    ellipsis={{
                                      rows: 2,
                                      expandable: true,
                                      symbol: '展开',
                                    }}
                                  >
                                    {relation.record}
                                  </Typography.Paragraph>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </Collapse.Panel>
                    ))}
                  </Collapse>
                </>
              )}
            </Card>

            {/* 关系网络图 */}
            {relations.length > 0 && detail && (
              <ElementNetworkGraph
                relations={relations}
                currentElement={{
                  id: numericId,
                  name: detail.name || '当前要素',
                  type: type || 'unknown',
                }}
                loading={relationsLoading}
                height={500}
                title="关系网络图"
                className="detail-card network-graph"
                style={{ marginTop: '16px' }}
                onNodeClick={(nodeData) => {
                  console.log('节点点击:', nodeData);
                  // 可以在这里实现节点点击跳转到对应要素详情
                }}
              />
            )}
          </div>
        </div>
      </PublicLayout>
    </div>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  regionList: dictionary.regionList,
  typeList: dictionary.typeList,
}))(DetailPage);
