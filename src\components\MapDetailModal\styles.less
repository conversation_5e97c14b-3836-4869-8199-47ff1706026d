/* MapDetailModal 组件样式 */

.map-detail-modal {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
  }

  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;

    .ant-modal-title {
      color: #fff;
      font-weight: 600;
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: #fff;

      &:hover {
        color: rgba(255, 255, 255, 80%);
      }
    }
  }

  .ant-modal-body {
    padding: 0;

    // 主滚动容器
    > div {
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;

        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }
    }
  }

  // 关联信息卡片样式
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);

    .ant-card-head {
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #333;
      }
    }

    .ant-card-body {
      // 关联信息卡片的滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f5f5;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 3px;

        &:hover {
          background: #bfbfbf;
        }
      }
    }
  }

  // 折叠面板样式
  .ant-collapse {
    border: none;
    background: transparent;

    .ant-collapse-item {
      border: none;
      margin-bottom: 8px;

      .ant-collapse-header {
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        &:hover {
          background: #e9ecef;
        }
      }

      .ant-collapse-content {
        border: none;
        background: transparent;

        .ant-collapse-content-box {
          padding: 16px;
          background: #fff;
          border-radius: 0 0 6px 6px;
          border: 1px solid #e9ecef;
          border-top: none;
        }
      }
    }
  }

  // 标签样式
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  // 描述列表样式
  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 600;
      color: #666;
    }

    .ant-descriptions-item-content {
      color: #333;
    }
  }

  // 网络图卡片样式
  .network-graph-card {
    .ant-card-body {
      padding: 0;
      overflow: hidden;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .map-detail-modal {
    .ant-modal {
      margin: 0;
      max-width: 100vw;
      top: 0;
    }

    .ant-modal-content {
      border-radius: 0;
      height: 100vh;
    }

    .ant-modal-body {
      height: calc(100vh - 110px);

      > div {
        height: 100%;
      }
    }
  }
}
