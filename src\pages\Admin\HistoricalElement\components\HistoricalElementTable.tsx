import { DictionaryState } from '@/models/dictionary';
import { formatYear } from '@/utils/yearUtils';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { connect } from '@umijs/max';
import { Button, Popconfirm, Space, Table, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect } from 'react';

export interface HistoricalElementTableProps {
  data: API.HistoricalElement[];
  loading: boolean;
  selectedRowKeys: React.Key[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onView: (record: API.HistoricalElement) => void;
  onEdit: (record: API.HistoricalElement) => void;
  onDelete: (id: number) => void;
  onPaginationChange: (page: number, pageSize: number) => void;
  onShowSizeChange: (current: number, size: number) => void;
  dictionary?: any; // dva字典状态
  dispatch: any;
}

export const HistoricalElementTable: React.FC<HistoricalElementTableProps> = ({
  data,
  loading,
  selectedRowKeys,
  pagination,
  onSelectChange,
  onView,
  onEdit,
  onDelete,
  onPaginationChange,
  onShowSizeChange,
  dictionary,
  dispatch,
}) => {
  // 加载字典数据
  useEffect(() => {
    if (!dictionary?.typeList || dictionary.typeList.length === 0) {
      dispatch({ type: 'dictionary/fetchTypeList' });
    }
    if (!dictionary?.regionList || dictionary.regionList.length === 0) {
      dispatch({ type: 'dictionary/fetchRegionList' });
    }
  }, [dispatch, dictionary?.typeList, dictionary?.regionList]);
  // 表格列定义
  const columns: ColumnsType<API.HistoricalElement> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '历史要素名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left',
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: '类型',
      dataIndex: 'typeDictId',
      key: 'typeDictId',
      width: 100,
      render: (_: any, record: API.HistoricalElement) => {
        if (!record.typeDictId) {
          return <Tag color="default">未知类型</Tag>;
        }

        // 从字典数据中查找对应的类型名称（递归查找树形结构）
        const findTypeName = (typeList: any[], typeId: number): string => {
          for (const type of typeList) {
            if (type.id === typeId) {
              return type.typeName;
            }
            if (type.children && type.children.length > 0) {
              const childResult = findTypeName(type.children, typeId);
              if (childResult) return childResult;
            }
          }
          return '';
        };

        const typeName =
          findTypeName(dictionary?.typeList || [], record.typeDictId) ||
          `类型${record.typeDictId}`;

        return <Tag color="green">{typeName}</Tag>;
      },
    },
    {
      title: '建造年份',
      dataIndex: 'constructionYear',
      key: 'constructionYear',
      width: 120,
      render: (year: number) => (
        <span style={{ fontWeight: 500, color: '#1890ff' }}>
          {formatYear(year)}
        </span>
      ),
    },
    {
      title: '所属区域',
      dataIndex: 'regionDictId',
      key: 'regionDictId',
      width: 120,
      render: (_: any, record: API.HistoricalElement) => {
        if (!record.regionDictId) {
          return <Tag color="default">未知区域</Tag>;
        }

        // 从字典数据中查找对应的区域名称
        const findRegionName = (
          regionList: any[],
          regionId: number,
        ): string => {
          for (const region of regionList) {
            if (region.id === regionId) {
              return region.regionName;
            }
            if (region.children && region.children.length > 0) {
              const childResult = findRegionName(region.children, regionId);
              if (childResult) return childResult;
            }
          }
          return '';
        };

        const regionName =
          findRegionName(dictionary?.regionList || [], record.regionDictId) ||
          `区域${record.regionDictId}`;

        return <Tag color="orange">{regionName}</Tag>;
      },
    },
    {
      title: '经度',
      dataIndex: 'constructionLongitude',
      key: 'constructionLongitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>{value || '-'}</span>
      ),
    },
    {
      title: '纬度',
      dataIndex: 'constructionLatitude',
      key: 'constructionLatitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>{value || '-'}</span>
      ),
    },
    {
      title: '历史记载',
      dataIndex: 'historicalRecords',
      key: 'historicalRecords',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <span>{text || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {text ? new Date(text).toLocaleString() : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_: any, record: API.HistoricalElement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onView(record);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(record);
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个历史要素吗？"
              description="删除后将无法恢复，请谨慎操作。"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.HistoricalElement) => ({
      disabled: false,
      name: record.name,
    }),
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={rowSelection}
      scroll={{ x: 1600, y: 'calc(100vh - 400px)' }}
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100'],
        onChange: onPaginationChange,
        onShowSizeChange,
      }}
      size="small"
      bordered
      style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        overflow: 'hidden',
      }}
    />
  );
};

// 连接dva状态
export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  dictionary,
}))(HistoricalElementTable);
