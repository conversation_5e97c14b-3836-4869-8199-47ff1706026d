import React, { ReactNode, useEffect, useRef, useState } from 'react';
import './index.less';

interface FullPageScrollProps {
  children: ReactNode[];
  duration?: number;
  easing?: string;
  onSectionChange?: (index: number) => void;
  showNavigation?: boolean;
}

// 导出滚动函数供外部使用
let scrollToSectionExternal: (index: number) => void;

const FullPageScroll: React.FC<FullPageScrollProps> = ({
  children,
  duration = 1200,
  easing = 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  onSectionChange,
  showNavigation = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentSection, setCurrentSection] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [touchStartY, setTouchStartY] = useState(0);

  const totalSections = children.length;

  const scrollToSection = (index: number) => {
    if (index < 0 || index >= totalSections || isScrolling) return;

    setIsScrolling(true);
    setCurrentSection(index);
    onSectionChange?.(index);

    setTimeout(() => {
      setIsScrolling(false);
    }, duration);
  };

  const handleWheel = (e: WheelEvent) => {
    if (isScrolling) return;

    // 检查鼠标是否在地图容器内
    const target = e.target as HTMLElement;

    // 更精确的地图区域检测，包括抽屉组件
    const isInMapArea = target.closest(
      '#map-container, .amap-container, [id*="amap"], [class*="amap"]',
    );

    // 检查是否在抽屉组件内
    const isInDrawer = target.closest('.ant-drawer, .ant-modal');

    // 检查是否在可滚动容器内
    const isInScrollableContainer = target.closest(
      '[style*="overflow-y: auto"], [style*="overflowY: auto"], .ant-card-body',
    );

    // 如果鼠标在地图区域内、抽屉内或可滚动容器内，不拦截滚轮事件
    if (isInMapArea || isInDrawer || isInScrollableContainer) {
      // 阻止事件冒泡，但不阻止默认行为，让对应组件处理
      e.stopPropagation();
      return;
    }

    e.preventDefault();

    if (e.deltaY > 0) {
      // 向下滚动
      scrollToSection(currentSection + 1);
    } else {
      // 向上滚动
      scrollToSection(currentSection - 1);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (isScrolling) return;

    switch (e.key) {
      case 'ArrowDown':
      case 'PageDown':
      case ' ':
        e.preventDefault();
        scrollToSection(currentSection + 1);
        break;
      case 'ArrowUp':
      case 'PageUp':
        e.preventDefault();
        scrollToSection(currentSection - 1);
        break;
      case 'Home':
        e.preventDefault();
        scrollToSection(0);
        break;
      case 'End':
        e.preventDefault();
        scrollToSection(totalSections - 1);
        break;
    }
  };

  const handleTouchStart = (e: TouchEvent) => {
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchEnd = (e: TouchEvent) => {
    if (isScrolling) return;

    const touchEndY = e.changedTouches[0].clientY;
    const deltaY = touchStartY - touchEndY;

    // 最小滑动距离
    if (Math.abs(deltaY) > 50) {
      if (deltaY > 0) {
        // 向上滑动，显示下一屏
        scrollToSection(currentSection + 1);
      } else {
        // 向下滑动，显示上一屏
        scrollToSection(currentSection - 1);
      }
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 设置CSS变量
    container.style.setProperty('--scroll-duration', `${duration}ms`);
    container.style.setProperty('--scroll-easing', easing);

    // 设置外部滚动函数
    scrollToSectionExternal = scrollToSection;

    // 监听外部滚动事件
    const handleExternalScroll = (e: CustomEvent) => {
      const { section } = e.detail;
      scrollToSection(section);
    };

    // 添加事件监听
    window.addEventListener('wheel', handleWheel, { passive: false });
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('touchstart', handleTouchStart, { passive: true });
    window.addEventListener('touchend', handleTouchEnd, { passive: true });
    window.addEventListener(
      'fullpage-scroll-to',
      handleExternalScroll as EventListener,
    );

    // 防止默认滚动，但只在当前页面有效
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';

    return () => {
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);
      window.removeEventListener(
        'fullpage-scroll-to',
        handleExternalScroll as EventListener,
      );

      // 恢复原始的overflow样式，而不是强制设置为auto
      document.body.style.overflow = originalOverflow || 'auto';

      // 确保下拉框定位正确
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);
    };
  }, [currentSection, isScrolling, duration, easing]);

  return (
    <div className="fullpage-scroll-wrapper">
      <div
        ref={containerRef}
        className="fullpage-scroll-container"
        style={{
          transform: `translateY(-${
            currentSection * (window.innerHeight - 72)
          }px)`,
          transition: isScrolling
            ? `transform ${duration}ms ${easing}`
            : 'none',
        }}
      >
        {children.map((child, index) => (
          <div key={index} className="fullpage-section">
            {child}
          </div>
        ))}
      </div>

      {/* 导航指示器 */}
      <div className="fullpage-nav" style={showNavigation ? {} : {}}>
        {children.map((_, index) => (
          <button
            key={index}
            type="button"
            className={`nav-dot ${index === currentSection ? 'active' : ''}`}
            onClick={() => scrollToSection(index)}
            aria-label={`跳转到第${index + 1}屏`}
          />
        ))}
      </div>
    </div>
  );
};

// 导出滚动函数供外部使用
export const scrollToSection = (index: number) => {
  if (scrollToSectionExternal) {
    scrollToSectionExternal(index);
  }
};

export default FullPageScroll;
