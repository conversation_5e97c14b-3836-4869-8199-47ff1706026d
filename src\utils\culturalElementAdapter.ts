import {
  getCulturalElementMapData,
  getPublicCulturalElementList,
  getPublicCulturalElementDetail,
  getPublicCulturalElementPhotos,
  getPublicCulturalElementStatistics,
  getPublicCulturalElementsByType,
  getPublicCulturalElementsByRegion,
} from '@/services/culturalElement';
import { getTypeFromTypeName } from '@/services/mapConfig';

// ==================== 类型映射常量 ====================

/**
 * 类型字典ID映射关系
 * 注意：这些ID需要根据实际的类型字典数据进行调整
 */
export const TYPE_MAPPING = {
  // 历史要素相关类型ID（需要根据实际数据调整）
  HISTORICAL_ELEMENTS: [1, 2, 3, 4, 5],
  // 山塬相关类型ID（需要根据实际数据调整）
  MOUNTAINS: [6, 7, 8],
  // 水系相关类型ID（需要根据实际数据调整）
  WATER_SYSTEMS: [9, 10, 11],
} as const;

/**
 * 类型名称映射
 */
export const TYPE_NAME_MAPPING = {
  mountain: '山塬',
  waterSystem: '水系',
  historicalElement: '历史要素',
} as const;

/**
 * 反向类型映射：从typeDictId获取旧版本类型
 */
export const getOldTypeFromTypeDictId = (typeDictId?: number): 'mountain' | 'waterSystem' | 'historicalElement' | undefined => {
  if (!typeDictId) return undefined;
  
  if (TYPE_MAPPING.MOUNTAINS.includes(typeDictId)) {
    return 'mountain';
  }
  if (TYPE_MAPPING.WATER_SYSTEMS.includes(typeDictId)) {
    return 'waterSystem';
  }
  if (TYPE_MAPPING.HISTORICAL_ELEMENTS.includes(typeDictId)) {
    return 'historicalElement';
  }
  
  return undefined;
};

/**
 * 从类型名称获取typeDictId数组
 */
export const getTypeDictIdsFromOldType = (type: 'mountain' | 'waterSystem' | 'historicalElement'): number[] => {
  switch (type) {
    case 'mountain':
      return TYPE_MAPPING.MOUNTAINS;
    case 'waterSystem':
      return TYPE_MAPPING.WATER_SYSTEMS;
    case 'historicalElement':
      return TYPE_MAPPING.HISTORICAL_ELEMENTS;
    default:
      return [];
  }
};

// ==================== 数据格式转换函数 ====================

/**
 * 将文化要素转换为山塬格式
 */
export const convertCulturalElementToMountain = (element: API.CulturalElement): API.Mountain => {
  return {
    id: element.id,
    name: element.name,
    code: element.code,
    typeDictId: element.typeDictId,
    longitude: element.longitude,
    latitude: element.latitude,
    height: element.height || 0, // 山塬必须有高度
    locationDescription: element.locationDescription,
    historicalRecords: element.historicalRecords,
    regionDictId: element.regionDictId,
    typeDict: element.typeDict,
    regionDict: element.regionDict,
    photos: element.photos,
    createdAt: element.createdAt?.toString(),
    updatedAt: element.updatedAt?.toString(),
  };
};

/**
 * 将文化要素转换为水系格式
 */
export const convertCulturalElementToWaterSystem = (element: API.CulturalElement): API.WaterSystem => {
  return {
    id: element.id,
    name: element.name,
    code: element.code,
    typeDictId: element.typeDictId,
    longitude: element.longitude,
    latitude: element.latitude,
    lengthArea: element.lengthArea,
    locationDescription: element.locationDescription,
    historicalRecords: element.historicalRecords,
    regionDictId: element.regionDictId,
    typeDict: element.typeDict,
    regionDict: element.regionDict,
    photos: element.photos,
    createdAt: element.createdAt?.toString(),
    updatedAt: element.updatedAt?.toString(),
  };
};

/**
 * 将文化要素转换为历史要素格式
 */
export const convertCulturalElementToHistoricalElement = (element: API.CulturalElement): API.HistoricalElement => {
  return {
    id: element.id,
    name: element.name,
    code: element.code,
    typeDictId: element.typeDictId,
    constructionLongitude: element.longitude,
    constructionLatitude: element.latitude,
    locationDescription: element.locationDescription,
    constructionYear: element.constructionYear,
    historicalRecords: element.historicalRecords,
    regionDictId: element.regionDictId,
    typeDict: element.typeDict,
    regionDict: element.regionDict,
    photos: element.photos,
    createdAt: element.createdAt?.toString(),
    updatedAt: element.updatedAt?.toString(),
  };
};

/**
 * 按类型分组文化要素数据
 */
export const groupCulturalElementsByType = (elements: API.CulturalElement[]) => {
  const mountains: API.Mountain[] = [];
  const waterSystems: API.WaterSystem[] = [];
  const historicalElements: API.HistoricalElement[] = [];

  elements.forEach(element => {
    const oldType = getOldTypeFromTypeDictId(element.typeDictId);
    
    switch (oldType) {
      case 'mountain':
        mountains.push(convertCulturalElementToMountain(element));
        break;
      case 'waterSystem':
        waterSystems.push(convertCulturalElementToWaterSystem(element));
        break;
      case 'historicalElement':
        historicalElements.push(convertCulturalElementToHistoricalElement(element));
        break;
      default:
        // 如果无法确定类型，根据typeName进行判断
        if (element.typeName?.includes('山') || element.typeName?.includes('塬')) {
          mountains.push(convertCulturalElementToMountain(element));
        } else if (element.typeName?.includes('水') || element.typeName?.includes('河') || element.typeName?.includes('川')) {
          waterSystems.push(convertCulturalElementToWaterSystem(element));
        } else {
          historicalElements.push(convertCulturalElementToHistoricalElement(element));
        }
        break;
    }
  });

  return {
    mountains,
    waterSystems,
    historicalElements,
  };
};

// ==================== 查询参数转换函数 ====================

/**
 * 将旧版本查询参数转换为新版本
 */
export const convertOldParamsToNew = (
  params: API.GetMountainListParams | API.GetWaterSystemListParams | API.GetHistoricalElementListParams,
  type: 'mountain' | 'waterSystem' | 'historicalElement'
): API.CulturalElementQueryDTO => {
  const typeDictIds = getTypeDictIdsFromOldType(type);
  
  return {
    page: params.page,
    pageSize: params.pageSize,
    keyword: params.keyword,
    regionDictId: params.regionId, // 注意字段名的变化
    typeDictId: typeDictIds.length > 0 ? typeDictIds[0] : undefined, // 使用第一个类型ID
  };
};

/**
 * 将新版本列表响应转换为旧版本格式
 */
export const convertNewListResponseToOld = <T>(
  response: API.CulturalElementListResponse,
  converter: (element: API.CulturalElement) => T
): { list: T[]; total: number; page: number; pageSize: number } => {
  return {
    list: response.list.map(converter),
    total: response.total,
    page: response.page,
    pageSize: response.pageSize,
  };
};

// ==================== 适配器函数 ====================

/**
 * 山塬列表查询适配器
 */
export const getMountainListAdapter = async (
  params?: API.GetMountainListParams
): Promise<API.ResType<API.MountainListResponse>> => {
  try {
    const newParams: API.CulturalElementQueryDTO = {
      ...convertOldParamsToNew(params || {}, 'mountain'),
      // 可以传入多个山塬类型ID进行查询
    };

    const response = await getPublicCulturalElementList(newParams);
    
    if (response.errCode === 0 && response.data) {
      // 过滤出山塬类型的数据
      const filteredElements = response.data.list.filter(element => 
        TYPE_MAPPING.MOUNTAINS.includes(element.typeDictId || 0) ||
        element.typeName?.includes('山') || 
        element.typeName?.includes('塬')
      );

      const mountainList = filteredElements.map(convertCulturalElementToMountain);

      return {
        errCode: 0,
        data: {
          list: mountainList,
          total: mountainList.length, // 注意：这里的total可能不准确，因为是过滤后的结果
          page: response.data.page,
          pageSize: response.data.pageSize,
        },
      };
    }

    return response as any;
  } catch (error) {
    console.error('山塬列表查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 水系列表查询适配器
 */
export const getWaterSystemListAdapter = async (
  params?: API.GetWaterSystemListParams
): Promise<API.ResType<API.WaterSystemListResponse>> => {
  try {
    const newParams: API.CulturalElementQueryDTO = {
      ...convertOldParamsToNew(params || {}, 'waterSystem'),
    };

    const response = await getPublicCulturalElementList(newParams);

    if (response.errCode === 0 && response.data) {
      // 过滤出水系类型的数据
      const filteredElements = response.data.list.filter(element =>
        TYPE_MAPPING.WATER_SYSTEMS.includes(element.typeDictId || 0) ||
        element.typeName?.includes('水') ||
        element.typeName?.includes('河') ||
        element.typeName?.includes('川')
      );

      const waterSystemList = filteredElements.map(convertCulturalElementToWaterSystem);

      return {
        errCode: 0,
        data: {
          list: waterSystemList,
          total: waterSystemList.length,
          page: response.data.page,
          pageSize: response.data.pageSize,
        },
      };
    }

    return response as any;
  } catch (error) {
    console.error('水系列表查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 历史要素列表查询适配器
 */
export const getHistoricalElementListAdapter = async (
  params?: API.GetHistoricalElementListParams
): Promise<API.ResType<API.HistoricalElementListResponse>> => {
  try {
    const newParams: API.CulturalElementQueryDTO = {
      ...convertOldParamsToNew(params || {}, 'historicalElement'),
    };

    const response = await getPublicCulturalElementList(newParams);

    if (response.errCode === 0 && response.data) {
      // 过滤出历史要素类型的数据
      const filteredElements = response.data.list.filter(element =>
        TYPE_MAPPING.HISTORICAL_ELEMENTS.includes(element.typeDictId || 0) ||
        (!element.typeName?.includes('山') &&
         !element.typeName?.includes('塬') &&
         !element.typeName?.includes('水') &&
         !element.typeName?.includes('河') &&
         !element.typeName?.includes('川'))
      );

      const historicalElementList = filteredElements.map(convertCulturalElementToHistoricalElement);

      return {
        errCode: 0,
        data: {
          list: historicalElementList,
          total: historicalElementList.length,
          page: response.data.page,
          pageSize: response.data.pageSize,
        },
      };
    }

    return response as any;
  } catch (error) {
    console.error('历史要素列表查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 山塬详情查询适配器
 */
export const getMountainDetailAdapter = async (
  id: number
): Promise<API.ResType<API.Mountain>> => {
  try {
    const response = await getPublicCulturalElementDetail(id);

    if (response.errCode === 0 && response.data) {
      return {
        errCode: 0,
        data: convertCulturalElementToMountain(response.data),
      };
    }

    return response as any;
  } catch (error) {
    console.error('山塬详情查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 水系详情查询适配器
 */
export const getWaterSystemDetailAdapter = async (
  id: number
): Promise<API.ResType<API.WaterSystem>> => {
  try {
    const response = await getPublicCulturalElementDetail(id);

    if (response.errCode === 0 && response.data) {
      return {
        errCode: 0,
        data: convertCulturalElementToWaterSystem(response.data),
      };
    }

    return response as any;
  } catch (error) {
    console.error('水系详情查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 历史要素详情查询适配器
 */
export const getHistoricalElementDetailAdapter = async (
  id: number
): Promise<API.ResType<API.HistoricalElement>> => {
  try {
    const response = await getPublicCulturalElementDetail(id);

    if (response.errCode === 0 && response.data) {
      return {
        errCode: 0,
        data: convertCulturalElementToHistoricalElement(response.data),
      };
    }

    return response as any;
  } catch (error) {
    console.error('历史要素详情查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 照片查询适配器（通用）
 */
export const getElementPhotosAdapter = async (
  id: number
): Promise<API.ResType<Array<{ id: number; name: string; url: string }>>> => {
  try {
    return await getPublicCulturalElementPhotos(id);
  } catch (error) {
    console.error('照片查询适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

// ==================== 地图数据适配器 ====================

/**
 * 地图数据适配器 - 将新的统一地图数据转换为旧版本格式
 */
export const getMapDataAdapter = async (params?: {
  types?: ('mountain' | 'waterSystem' | 'historicalElement')[];
}) => {
  try {
    const response = await getCulturalElementMapData();

    if (response.errCode === 0 && response.data) {
      const { markers } = response.data;

      // 按类型分组标记点
      const groupedMarkers = groupCulturalElementsByType(
        markers.map(marker => ({
          id: marker.id,
          name: marker.name,
          code: marker.code,
          typeDictId: marker.extData.typeDictId,
          regionDictId: marker.extData.regionDictId,
          ancientCityId: marker.extData.ancientCityId,
          longitude: marker.longitude,
          latitude: marker.latitude,
          typeName: marker.typeName,
          regionName: marker.regionName,
          ancientCityName: marker.ancientCityName,
          constructionYear: marker.constructionYear,
          createdAt: new Date(),
          updatedAt: new Date(),
        }))
      );

      return {
        errCode: 0,
        data: {
          mountains: groupedMarkers.mountains,
          waterSystems: groupedMarkers.waterSystems,
          historicalElements: groupedMarkers.historicalElements,
        },
      };
    }

    return response;
  } catch (error) {
    console.error('地图数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

// ==================== 首页门户数据适配器 ====================

/**
 * 门户概览数据适配器 - 将新的统一统计数据转换为门户概览格式
 */
export const getPortalOverviewAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    // 使用新的统一统计接口
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为门户概览格式
      const portalData = {
        statistics: {
          mountain: stats.byType.filter(t =>
            TYPE_MAPPING.MOUNTAINS.includes(t.typeDictId) ||
            t.typeName?.includes('山') ||
            t.typeName?.includes('塬')
          ).reduce((sum, t) => sum + t.count, 0),
          waterSystem: stats.byType.filter(t =>
            TYPE_MAPPING.WATER_SYSTEMS.includes(t.typeDictId) ||
            t.typeName?.includes('水') ||
            t.typeName?.includes('河') ||
            t.typeName?.includes('川')
          ).reduce((sum, t) => sum + t.count, 0),
          historicalElement: stats.byType.filter(t =>
            TYPE_MAPPING.HISTORICAL_ELEMENTS.includes(t.typeDictId) ||
            (!t.typeName?.includes('山') &&
             !t.typeName?.includes('塬') &&
             !t.typeName?.includes('水') &&
             !t.typeName?.includes('河') &&
             !t.typeName?.includes('川'))
          ).reduce((sum, t) => sum + t.count, 0),
        },
        regionDistribution: stats.byRegion.map(r => ({
          region: r.regionName,
          regionId: r.regionDictId,
          mountainCount: 0, // 需要进一步细分统计
          waterSystemCount: 0,
          historicalElementCount: 0,
          total: r.count,
        })),
        recentData: {
          mountains: [],
          waterSystems: [],
          historicalElements: [],
        },
      };

      return {
        errCode: 0,
        data: portalData,
      };
    }

    return response as any;
  } catch (error) {
    console.error('门户概览数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 门户地图标记点适配器 - 将新的统一地图数据转换为门户地图标记点格式
 */
export const getPortalMapMarkersAdapter = async (params?: {
  types?: Array<'mountain' | 'waterSystem' | 'historicalElement'>;
  regionId?: number;
}): Promise<API.ResType<any[]>> => {
  try {
    // 构建查询参数
    const queryParams: API.MapDataQueryDTO = {};
    if (params?.regionId) {
      queryParams.regionDictId = params.regionId;
    }

    const response = await getCulturalElementMapData(queryParams);

    if (response.errCode === 0 && response.data) {
      const { markers } = response.data;

      // 转换为门户地图标记点格式
      const portalMarkers = markers
        .filter(marker => {
          // 如果指定了类型过滤，则进行过滤
          if (params?.types && params.types.length > 0) {
            const oldType = getOldTypeFromTypeDictId(marker.extData.typeDictId);
            return oldType && params.types.includes(oldType);
          }
          return true;
        })
        .map(marker => {
          const oldType = getOldTypeFromTypeDictId(marker.extData.typeDictId) ||
                          getTypeFromTypeName(marker.typeName);

          return {
            id: marker.id,
            type: oldType,
            name: marker.name,
            longitude: marker.longitude,
            latitude: marker.latitude,
            thumbnailUrl: undefined, // 需要从照片接口获取
            summary: marker.regionName || marker.ancientCityName,
          };
        });

      return {
        errCode: 0,
        data: portalMarkers,
      };
    }

    return response as any;
  } catch (error) {
    console.error('门户地图标记点适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

// ==================== 数字化统计数据适配器 ====================

/**
 * 基础统计数据适配器 - 将新的统一统计数据转换为基础统计格式
 */
export const getBasicStatisticsAdapter = async (params?: {
  regionId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<API.ResType<any>> => {
  try {
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为基础统计格式
      const basicStats = {
        counts: {
          mountain: stats.byType.filter(t =>
            TYPE_MAPPING.MOUNTAINS.includes(t.typeDictId) ||
            t.typeName?.includes('山') ||
            t.typeName?.includes('塬')
          ).reduce((sum, t) => sum + t.count, 0),
          waterSystem: stats.byType.filter(t =>
            TYPE_MAPPING.WATER_SYSTEMS.includes(t.typeDictId) ||
            t.typeName?.includes('水') ||
            t.typeName?.includes('河') ||
            t.typeName?.includes('川')
          ).reduce((sum, t) => sum + t.count, 0),
          historicalElement: stats.byType.filter(t =>
            TYPE_MAPPING.HISTORICAL_ELEMENTS.includes(t.typeDictId) ||
            (!t.typeName?.includes('山') &&
             !t.typeName?.includes('塬') &&
             !t.typeName?.includes('水') &&
             !t.typeName?.includes('河') &&
             !t.typeName?.includes('川'))
          ).reduce((sum, t) => sum + t.count, 0),
          user: 0, // 用户数据需要从其他接口获取
          typeDict: stats.byType.length,
          regionDict: stats.byRegion.length,
          relationshipDict: 0, // 关系字典数据需要从其他接口获取
        },
        regionStats: stats.byRegion.map(r => ({
          region: r.regionName,
          regionId: r.regionDictId,
          mountainCount: 0, // 需要进一步细分统计
          waterSystemCount: 0,
          historicalElementCount: 0,
          total: r.count,
        })),
        timelineData: stats.byConstructionYear.map(y => ({
          year: y.year,
          elements: [], // 具体元素列表需要从其他接口获取
        })),
      };

      return {
        errCode: 0,
        data: basicStats,
      };
    }

    return response as any;
  } catch (error) {
    console.error('基础统计数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 概览统计数据适配器 - 将新的统一统计数据转换为概览统计格式
 */
export const getOverviewStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为概览统计格式
      const overviewStats = {
        totalCounts: {
          mountain: stats.byType.filter(t =>
            TYPE_MAPPING.MOUNTAINS.includes(t.typeDictId) ||
            t.typeName?.includes('山') ||
            t.typeName?.includes('塬')
          ).reduce((sum, t) => sum + t.count, 0),
          waterSystem: stats.byType.filter(t =>
            TYPE_MAPPING.WATER_SYSTEMS.includes(t.typeDictId) ||
            t.typeName?.includes('水') ||
            t.typeName?.includes('河') ||
            t.typeName?.includes('川')
          ).reduce((sum, t) => sum + t.count, 0),
          historicalElement: stats.byType.filter(t =>
            TYPE_MAPPING.HISTORICAL_ELEMENTS.includes(t.typeDictId) ||
            (!t.typeName?.includes('山') &&
             !t.typeName?.includes('塬') &&
             !t.typeName?.includes('水') &&
             !t.typeName?.includes('河') &&
             !t.typeName?.includes('川'))
          ).reduce((sum, t) => sum + t.count, 0),
          user: 0,
          typeDict: stats.byType.length,
          regionDict: stats.byRegion.length,
          relationshipDict: 0,
        },
        regionStats: stats.byRegion.map(r => ({
          region: r.regionName,
          regionId: r.regionDictId,
          mountainCount: 0,
          waterSystemCount: 0,
          historicalElementCount: 0,
          total: r.count,
        })),
        timelineData: stats.byConstructionYear.map(y => ({
          year: y.year,
          elements: [],
        })),
      };

      return {
        errCode: 0,
        data: overviewStats,
      };
    }

    return response as any;
  } catch (error) {
    console.error('概览统计数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 区域分布统计适配器 - 将新的统一统计数据转换为区域分布格式
 */
export const getRegionDistributionStatisticsAdapter = async (): Promise<API.ResType<any[]>> => {
  try {
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为区域分布格式
      const regionStats = stats.byRegion.map(r => ({
        region: r.regionName,
        regionId: r.regionDictId,
        mountainCount: 0, // 需要进一步细分统计
        waterSystemCount: 0,
        historicalElementCount: 0,
        total: r.count,
      }));

      return {
        errCode: 0,
        data: regionStats,
      };
    }

    return response as any;
  } catch (error) {
    console.error('区域分布统计适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 时间轴统计适配器 - 将新的统一统计数据转换为时间轴格式
 */
export const getTimelineStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any[]>> => {
  try {
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为时间轴格式
      const timelineStats = stats.byConstructionYear.map(y => ({
        year: y.year,
        elements: [], // 具体元素列表需要从其他接口获取
      }));

      return {
        errCode: 0,
        data: timelineStats,
      };
    }

    return response as any;
  } catch (error) {
    console.error('时间轴统计适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};

/**
 * 综合统计数据适配器 - 将新的统一统计数据转换为综合统计格式
 */
export const getComprehensiveStatisticsAdapter = async (params?: {
  regionId?: number;
}): Promise<API.ResType<any>> => {
  try {
    const response = await getPublicCulturalElementStatistics();

    if (response.errCode === 0 && response.data) {
      const stats = response.data;

      // 转换为综合统计格式（可以根据需要扩展）
      const comprehensiveStats = {
        summary: {
          total: stats.total,
          byType: stats.byType,
          byRegion: stats.byRegion,
          byAncientCity: stats.byAncientCity,
          byConstructionYear: stats.byConstructionYear,
        },
        trends: {
          // 可以添加趋势分析数据
        },
        insights: {
          // 可以添加洞察分析数据
        },
      };

      return {
        errCode: 0,
        data: comprehensiveStats,
      };
    }

    return response as any;
  } catch (error) {
    console.error('综合统计数据适配器错误:', error);
    return {
      errCode: -1,
      msg: '查询失败',
    };
  }
};
