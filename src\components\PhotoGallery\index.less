.photo-gallery {
  .photo-gallery-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    
    .photo-item {
      position: relative;
      
      .photo-wrapper {
        position: relative;
        width: 100%;
        height: 150px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #d9d9d9;
        
        .photo-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        .photo-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          
          .ant-btn {
            color: white;
            border-color: white;
            
            &:hover {
              color: #1890ff;
              border-color: #1890ff;
              background: white;
            }
          }
        }
        
        &:hover {
          .photo-image {
            transform: scale(1.05);
          }
          
          .photo-overlay {
            opacity: 1;
          }
        }
      }
      
      .photo-name {
        margin-top: 8px;
        text-align: center;
        font-size: 12px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      &.upload-item {
        .upload-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 150px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: #fafafa;
          cursor: pointer;
          transition: all 0.3s ease;
          
          .anticon {
            font-size: 24px;
            color: #999;
            margin-bottom: 8px;
          }
          
          div {
            color: #999;
            font-size: 14px;
          }
          
          &:hover {
            border-color: #1890ff;
            background: #f0f8ff;
            
            .anticon,
            div {
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .photo-gallery {
    .photo-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;
      
      .photo-item .photo-wrapper {
        height: 120px;
      }
      
      .photo-item.upload-item .upload-wrapper {
        height: 120px;
      }
    }
  }
}
