import { DeleteOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Empty, Image, Modal, Space, Spin, Upload, message } from 'antd';
import React, { useState } from 'react';
import './index.less';

export interface PhotoGalleryProps {
  photos: API.ElementPhoto[];
  loading?: boolean;
  entityType: string;
  entityId: number;
  onUpdate?: () => void;
  editable?: boolean;
  maxCount?: number;
}

const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  loading = false,
  entityType,
  entityId,
  onUpdate,
  editable = true,
  maxCount = 10,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [uploading, setUploading] = useState(false);

  // 预览图片
  const handlePreview = (photo: API.ElementPhoto) => {
    setPreviewImage(photo.url);
    setPreviewTitle(photo.name || '图片预览');
    setPreviewVisible(true);
  };

  // 删除图片
  const handleDelete = async (photoId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          // 这里应该调用删除照片的API
          // await deleteElementPhoto(photoId);
          message.success('删除成功');
          onUpdate?.();
        } catch (error: any) {
          console.error('删除照片失败:', error);
          message.error(error?.message || '删除失败');
        }
      },
    });
  };

  // 上传图片
  const handleUpload = async (file: File) => {
    setUploading(true);
    try {
      // 这里应该调用上传照片的API
      // await uploadElementPhoto(entityType, entityId, file);
      message.success('上传成功');
      onUpdate?.();
    } catch (error: any) {
      console.error('上传照片失败:', error);
      message.error(error?.message || '上传失败');
    } finally {
      setUploading(false);
    }
  };

  if (loading) {
    return (
      <div className="photo-gallery-loading">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="photo-gallery">
      <div className="photo-grid">
        {photos.map((photo) => (
          <div key={photo.id} className="photo-item">
            <div className="photo-wrapper">
              <img
                src={photo.url}
                alt={photo.name}
                className="photo-image"
              />
              <div className="photo-overlay">
                <Space>
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(photo)}
                  />
                  {editable && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDelete(photo.id)}
                    />
                  )}
                </Space>
              </div>
            </div>
            {photo.name && (
              <div className="photo-name">{photo.name}</div>
            )}
          </div>
        ))}

        {/* 上传按钮 */}
        {editable && photos.length < maxCount && (
          <div className="photo-item upload-item">
            <Upload
              accept="image/*"
              showUploadList={false}
              beforeUpload={(file) => {
                handleUpload(file);
                return false;
              }}
            >
              <div className="upload-wrapper">
                <PlusOutlined />
                <div>上传图片</div>
              </div>
            </Upload>
          </div>
        )}
      </div>

      {photos.length === 0 && !editable && (
        <Empty description="暂无图片" />
      )}

      {/* 图片预览 */}
      <Image
        style={{ display: 'none' }}
        preview={{
          visible: previewVisible,
          onVisibleChange: setPreviewVisible,
          src: previewImage,
        }}
      />
    </div>
  );
};

export default PhotoGallery;
