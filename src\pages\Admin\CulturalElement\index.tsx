import DictSelect from '@/components/DictSelect';
import PermissionWrapper from '@/components/PermissionWrapper';
import PhotoBatchActions from '@/components/PhotoBatchActions';
import UploadButton from '@/components/UploadButton';
import YearRangePicker from '@/components/YearRangePicker';
import { getCulturalElementList, deleteCulturalElement } from '@/services/culturalElement';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import useCulturalElementData from './hooks/useCulturalElementData';
import useCulturalElementOperations from './hooks/useCulturalElementOperations';
import './index.less';

const CulturalElementManagement: React.FC = () => {
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 使用自定义Hook管理数据
  const {
    data,
    statistics,
    loading,
    statisticsLoading,
    pagination,
    fetchData,
    fetchStatistics,
    setPagination,
  } = useCulturalElementData();

  // 使用自定义Hook管理操作
  const {
    deleteLoading,
    batchDeleteLoading,
    importLoading,
    exportLoading,
    handleDelete,
    handleBatchDelete,
    handleImport,
    handleExport,
    handleDownloadTemplate,
  } = useCulturalElementOperations();

  // 搜索表单提交
  const handleSearch = async (values: any) => {
    const params = {
      ...values,
      page: 1,
      pageSize: pagination.pageSize,
    };
    await fetchData(
      params.page,
      params.pageSize,
      params.keyword,
      params.regionDictId,
      params.typeDictId,
      params.ancientCityId,
      params.constructionYear,
    );
  };

  // 重置搜索
  const handleReset = async () => {
    form.resetFields();
    await fetchData(1, pagination.pageSize);
  };

  // 表格列定义
  const columns: ColumnsType<API.CulturalElement> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
      render: (text: string, record: API.CulturalElement) => (
        <Tooltip title={text}>
          <Button
            type="link"
            onClick={() => history.push(`/admin/cultural-element/detail/${record.id}`)}
          >
            {text}
          </Button>
        </Tooltip>
      ),
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'typeName',
      key: 'typeName',
      width: 100,
      render: (text: string, record: API.CulturalElement) => (
        <Tag color="blue">{text || record.typeDict?.typeName || '-'}</Tag>
      ),
    },
    {
      title: '区域',
      dataIndex: 'regionName',
      key: 'regionName',
      width: 120,
      render: (text: string, record: API.CulturalElement) => (
        <Tag color="green">{text || record.regionDict?.regionName || '-'}</Tag>
      ),
    },
    {
      title: '古城',
      dataIndex: 'ancientCityName',
      key: 'ancientCityName',
      width: 120,
      render: (text: string, record: API.CulturalElement) => (
        <Tag color="orange">{text || record.ancientCity?.name || '-'}</Tag>
      ),
    },
    {
      title: '位置',
      key: 'location',
      width: 150,
      render: (_, record: API.CulturalElement) => {
        if (record.longitude && record.latitude) {
          return `${record.longitude.toFixed(4)}, ${record.latitude.toFixed(4)}`;
        }
        return '-';
      },
    },
    {
      title: '建造年份',
      dataIndex: 'constructionYear',
      key: 'constructionYear',
      width: 100,
      render: (year: number) => year || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: Date) => {
        if (!date) return '-';
        return new Date(date).toLocaleDateString();
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: API.CulturalElement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => history.push(`/admin/cultural-element/detail/${record.id}`)}
            />
          </Tooltip>
          <PermissionWrapper permission="canEdit">
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => history.push(`/admin/cultural-element/edit/${record.id}`)}
              />
            </Tooltip>
          </PermissionWrapper>
          <PermissionWrapper permission="canDelete">
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                loading={deleteLoading}
                onClick={() => handleDelete(record.id, record.name, fetchData)}
              />
            </Tooltip>
          </PermissionWrapper>
        </Space>
      ),
    },
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 分页变化处理
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination(prev => ({ ...prev, current: page, pageSize }));
    const formValues = form.getFieldsValue();
    fetchData(
      page,
      pageSize,
      formValues.keyword,
      formValues.regionDictId,
      formValues.typeDictId,
      formValues.ancientCityId,
      formValues.constructionYear,
    );
  };

  // 初始化数据
  useEffect(() => {
    fetchData();
    fetchStatistics();
  }, [fetchData, fetchStatistics]);

  return (
    <div className="cultural-element-management">
      {/* 页面标题和统计信息 */}
      <Card className="page-header-card" style={{ marginBottom: 16 }}>
        <div className="page-header">
          <div className="header-content">
            <h1 className="page-title">文化要素管理</h1>
            <p className="page-description">统一管理关中地区的山塬、水系、历史要素等文化要素信息</p>
          </div>
          {statistics && (
            <div className="statistics-info">
              <div className="stat-item">
                <span className="stat-value">{statistics.total}</span>
                <span className="stat-label">总数</span>
              </div>
              {statistics.byType.slice(0, 3).map((item, index) => (
                <div key={index} className="stat-item">
                  <span className="stat-value">{item.count}</span>
                  <span className="stat-label">{item.typeName}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
      {/* 搜索表单 */}
      <Card className="search-card" style={{ marginBottom: 16 }}>
        <Form form={form} onFinish={handleSearch} layout="vertical">
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="关键词" name="keyword">
                <Input placeholder="请输入名称或编号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="类型" name="typeDictId">
                <DictSelect.DictTreeSelect
                  type="type"
                  placeholder="请选择类型"
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="区域" name="regionDictId">
                <DictSelect.DictTreeSelect
                  type="region"
                  placeholder="请选择区域"
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="古城" name="ancientCityId">
                <DictSelect.DictTreeSelect
                  type="relation"
                  placeholder="请选择古城"
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="建造年份" name="constructionYear">
                <YearRangePicker placeholder={["起始年份", "结束年份"]} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label=" " style={{ marginBottom: 0 }}>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <PermissionWrapper permission="canEdit">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => history.push('/admin/cultural-element/create')}
                >
                  新增文化要素
                </Button>
              </PermissionWrapper>
              <PermissionWrapper permission="canDelete">
                <Button
                  danger
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteLoading}
                  onClick={() => handleBatchDelete(selectedRowKeys, fetchData, () => setSelectedRowKeys([]))}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </PermissionWrapper>
            </Space>
          </Col>
          <Col>
            <Space>
              <PermissionWrapper permission="canImport">
                <UploadButton
                  accept=".xlsx,.xls"
                  buttonText="导入数据"
                  onSuccess={(fileInfo) => {
                    // 这里应该处理文件上传成功后的导入逻辑
                    message.success('文件上传成功，开始导入数据');
                    fetchData();
                  }}
                  onError={(error) => {
                    message.error('文件上传失败');
                  }}
                />
              </PermissionWrapper>
              <PermissionWrapper permission="canExport">
                <Button
                  loading={exportLoading}
                  onClick={() => handleExport(form.getFieldsValue())}
                >
                  导出数据
                </Button>
              </PermissionWrapper>
              <Button onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 照片批量操作组件 */}
      <PhotoBatchActions
        selectedIds={selectedRowKeys as number[]}
        entityType="culturalElement"
        onSuccess={() => {
          fetchData();
          setSelectedRowKeys([]);
        }}
      />
    </div>
  );
};

export default CulturalElementManagement;
